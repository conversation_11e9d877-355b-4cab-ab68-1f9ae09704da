/**
 * Sync Event Constants for apphero-sf-sync-service
 * Defines event types and constants specific to sync operations for consistent logging
 */

const SYNC_EVENTS = {
  // General sync events
  SYNC_STARTED: 'SYNC_STARTED',
  SYNC_COMPLETED: 'SY<PERSON>_COMPLETED',
  <PERSON><PERSON><PERSON>_FAILED: 'SYNC_FAILED',
  SY<PERSON>_PARTIAL_SUCCESS: 'SY<PERSON>_PARTIAL_SUCCESS',

  // On-demand sync events
  ON_DEMAND_SYNC_INITIATED: 'ON_DEMAND_SYNC_INITIATED',
  ON_DEMAND_SYNC_COMPLETED: 'ON_DEMAND_SYNC_COMPLETED',
  ON_DEMAND_SYNC_FAILED: 'ON_DEMAND_SYNC_FAILED',

  // CMS content sync events
  CMS_SYNC_INITIATED: 'CMS_SYNC_INITIATED',
  CMS_SYNC_COMPLETED: 'CMS_SYNC_COMPLETED',
  C<PERSON>_SYNC_FAILED: 'CMS_SYNC_FAILED',
  <PERSON><PERSON>_DATA_FETCH_SUCCESS: 'C<PERSON>_<PERSON><PERSON><PERSON>_FETCH_SUCCESS',
  <PERSON><PERSON>_DATA_FETCH_FAILED: 'CMS_DATA_FETCH_FAILED',
  CMS_BULK_UPSERT_SUCCESS: 'CMS_BULK_UPSERT_SUCCESS',
  CMS_BULK_UPSERT_FAILED: 'CMS_BULK_UPSERT_FAILED',

  // Entity-specific sync events
  ACCOUNT_SYNC_INITIATED: 'ACCOUNT_SYNC_INITIATED',
  ACCOUNT_SYNC_COMPLETED: 'ACCOUNT_SYNC_COMPLETED',
  ACCOUNT_SYNC_FAILED: 'ACCOUNT_SYNC_FAILED',
  ACCOUNT_QUERY_SUCCESS: 'ACCOUNT_QUERY_SUCCESS',
  ACCOUNT_QUERY_FAILED: 'ACCOUNT_QUERY_FAILED',
  ACCOUNT_UPDATE_SUCCESS: 'ACCOUNT_UPDATE_SUCCESS',
  ACCOUNT_UPDATE_FAILED: 'ACCOUNT_UPDATE_FAILED',

  OPPORTUNITY_SYNC_INITIATED: 'OPPORTUNITY_SYNC_INITIATED',
  OPPORTUNITY_SYNC_COMPLETED: 'OPPORTUNITY_SYNC_COMPLETED',
  OPPORTUNITY_SYNC_FAILED: 'OPPORTUNITY_SYNC_FAILED',
  OPPORTUNITY_QUERY_SUCCESS: 'OPPORTUNITY_QUERY_SUCCESS',
  OPPORTUNITY_QUERY_FAILED: 'OPPORTUNITY_QUERY_FAILED',
  OPPORTUNITY_UPDATE_SUCCESS: 'OPPORTUNITY_UPDATE_SUCCESS',
  OPPORTUNITY_UPDATE_FAILED: 'OPPORTUNITY_UPDATE_FAILED',

  TASK_SYNC_INITIATED: 'TASK_SYNC_INITIATED',
  TASK_SYNC_COMPLETED: 'TASK_SYNC_COMPLETED',
  TASK_SYNC_FAILED: 'TASK_SYNC_FAILED',
  TASK_QUERY_SUCCESS: 'TASK_QUERY_SUCCESS',
  TASK_QUERY_FAILED: 'TASK_QUERY_FAILED',
  TASK_UPDATE_SUCCESS: 'TASK_UPDATE_SUCCESS',
  TASK_UPDATE_FAILED: 'TASK_UPDATE_FAILED',

  OPPORTUNITY_FILE_SYNC_INITIATED: 'OPPORTUNITY_FILE_SYNC_INITIATED',
  OPPORTUNITY_FILE_SYNC_COMPLETED: 'OPPORTUNITY_FILE_SYNC_COMPLETED',
  OPPORTUNITY_FILE_SYNC_FAILED: 'OPPORTUNITY_FILE_SYNC_FAILED',
  OPPORTUNITY_FILE_QUERY_SUCCESS: 'OPPORTUNITY_FILE_QUERY_SUCCESS',
  OPPORTUNITY_FILE_QUERY_FAILED: 'OPPORTUNITY_FILE_QUERY_FAILED',
  OPPORTUNITY_FILE_UPDATE_SUCCESS: 'OPPORTUNITY_FILE_UPDATE_SUCCESS',
  OPPORTUNITY_FILE_UPDATE_FAILED: 'OPPORTUNITY_FILE_UPDATE_FAILED',

  APPLICATION_SYNC_INITIATED: 'APPLICATION_SYNC_INITIATED',
  APPLICATION_SYNC_COMPLETED: 'APPLICATION_SYNC_COMPLETED',
  APPLICATION_SYNC_FAILED: 'APPLICATION_SYNC_FAILED',
  APPLICATION_QUERY_SUCCESS: 'APPLICATION_QUERY_SUCCESS',
  APPLICATION_QUERY_FAILED: 'APPLICATION_QUERY_FAILED',
  APPLICATION_UPDATE_SUCCESS: 'APPLICATION_UPDATE_SUCCESS',
  APPLICATION_UPDATE_FAILED: 'APPLICATION_UPDATE_FAILED',

  // Database operations
  DYNAMODB_SCAN_SUCCESS: 'DYNAMODB_SCAN_SUCCESS',
  DYNAMODB_SCAN_FAILED: 'DYNAMODB_SCAN_FAILED',
  DYNAMODB_UPDATE_SUCCESS: 'DYNAMODB_UPDATE_SUCCESS',
  DYNAMODB_UPDATE_FAILED: 'DYNAMODB_UPDATE_FAILED',
  DYNAMODB_CREATE_SUCCESS: 'DYNAMODB_CREATE_SUCCESS',
  DYNAMODB_CREATE_FAILED: 'DYNAMODB_CREATE_FAILED',
  DYNAMODB_BULK_UPSERT_SUCCESS: 'DYNAMODB_BULK_UPSERT_SUCCESS',
  DYNAMODB_BULK_UPSERT_FAILED: 'DYNAMODB_BULK_UPSERT_FAILED',

  // Salesforce operations
  SALESFORCE_QUERY_SUCCESS: 'SALESFORCE_QUERY_SUCCESS',
  SALESFORCE_QUERY_FAILED: 'SALESFORCE_QUERY_FAILED',
  SALESFORCE_COMPOSITE_REQUEST_SUCCESS: 'SALESFORCE_COMPOSITE_REQUEST_SUCCESS',
  SALESFORCE_COMPOSITE_REQUEST_FAILED: 'SALESFORCE_COMPOSITE_REQUEST_FAILED',
  SALESFORCE_API_CALL_SUCCESS: 'SALESFORCE_API_CALL_SUCCESS',
  SALESFORCE_API_CALL_FAILED: 'SALESFORCE_API_CALL_FAILED',

  // Error accumulation events
  ERROR_ACCUMULATED: 'ERROR_ACCUMULATED',
  ERROR_SUMMARY_GENERATED: 'ERROR_SUMMARY_GENERATED',
  COMPREHENSIVE_ERROR_REPORT: 'COMPREHENSIVE_ERROR_REPORT',

  // Batch processing events
  BATCH_PROCESSING_STARTED: 'BATCH_PROCESSING_STARTED',
  BATCH_PROCESSING_COMPLETED: 'BATCH_PROCESSING_COMPLETED',
  BATCH_PROCESSING_FAILED: 'BATCH_PROCESSING_FAILED',
  BATCH_ITEM_SUCCESS: 'BATCH_ITEM_SUCCESS',
  BATCH_ITEM_FAILED: 'BATCH_ITEM_FAILED',

  // Notification events
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  NOTIFICATION_FAILED: 'NOTIFICATION_FAILED',
};

const SYNC_OPERATIONS = {
  SYNC_ON_DEMAND: 'SYNC_ON_DEMAND',
  SYNC_CMS_CONTENT: 'SYNC_CMS_CONTENT',
  SYNC_PREFILL: 'SYNC_PREFILL',
  EXPORT_LOGS: 'EXPORT_LOGS',
  PROCESS_MAILER_EVENTS: 'PROCESS_MAILER_EVENTS',
  PROCESS_NOTIFICATIONS: 'PROCESS_NOTIFICATIONS',
};

const ENTITY_TYPES = {
  ACCOUNT: 'Account',
  OPPORTUNITY: 'Opportunity',
  TASK: 'Task',
  OPPORTUNITY_FILE: 'OpportunityFile',
  APPLICATION: 'Application__c',
  CMS_CONTENT: 'CMS_Content',
  CMS_EVENT: 'CMS_Event',
};

const SYNC_COMPONENTS = {
  SYNC_SERVICE: 'APPHERO_SF_SYNC_SERVICE',
  SALESFORCE: 'SALESFORCE',
  DYNAMODB: 'DYNAMODB',
  CMS: 'CMS',
};

const SYNC_USE_CASES = {
  ON_DEMAND_SYNC: 'ON_DEMAND_SYNC',
  CMS_CONTENT_SYNC: 'CMS_CONTENT_SYNC',
  SCHEDULED_SYNC: 'SCHEDULED_SYNC',
  MANUAL_SYNC: 'MANUAL_SYNC',
};

const ERROR_TYPES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  SALESFORCE_ERROR: 'SALESFORCE_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

const LOG_LEVELS = {
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  DEBUG: 'DEBUG',
};

/**
 * Helper function to create a standardized event name
 * @param {string} operation - The operation being performed
 * @param {string} entityType - The entity type
 * @param {string} action - The action (INITIATED, COMPLETED, FAILED)
 * @returns {string} Standardized event name
 */
function createEventName(operation, entityType, action) {
  return `${operation}_${entityType}_${action}`.toUpperCase();
}

/**
 * Helper function to get entity-specific events
 * @param {string} entityType - The entity type
 * @returns {Object} Object containing entity-specific events
 */
function getEntityEvents(entityType) {
  const upperEntityType = entityType.toUpperCase().replace('__C', '');
  return {
    SYNC_INITIATED: `${upperEntityType}_SYNC_INITIATED`,
    SYNC_COMPLETED: `${upperEntityType}_SYNC_COMPLETED`,
    SYNC_FAILED: `${upperEntityType}_SYNC_FAILED`,
    QUERY_SUCCESS: `${upperEntityType}_QUERY_SUCCESS`,
    QUERY_FAILED: `${upperEntityType}_QUERY_FAILED`,
    UPDATE_SUCCESS: `${upperEntityType}_UPDATE_SUCCESS`,
    UPDATE_FAILED: `${upperEntityType}_UPDATE_FAILED`,
  };
}

module.exports = {
  SYNC_EVENTS,
  SYNC_OPERATIONS,
  ENTITY_TYPES,
  SYNC_COMPONENTS,
  SYNC_USE_CASES,
  ERROR_TYPES,
  LOG_LEVELS,
  createEventName,
  getEntityEvents,
};
