const salesforce = require("../common/salesforce.service");
const { v4: uuidv4 } = require("uuid");
const {
  DynamoDBClient,
  TransactWriteItemsCommand,
} = require("@aws-sdk/client-dynamodb");

module.exports.handler = async (event) => {
  try {
    const cmsData = await getCMSData(process.env.APPHERO_CMS_SYNC_CHANNEL_ID);
    const cmsFeedData = cmsData.items.filter(
      (item) =>
        (item.contentNodes.Is_Active?.value == "1" ||
          item.contentNodes.Is_Active?.value == "yes") &&
        item.type == "App_Hero"
    );
    const cmsEventData = cmsData.items.filter((item) => item.type == "Event");
    const transformedCmsFeedData = updateMappingCountryInstitution(cmsFeedData);
    const transformedCmsEventData = constructCMSEventObject(cmsEventData);

    return bulkUpsert(process.env.APPHERO_CMS_SYNC_TABLE_NAME, [
      ...transformedCmsFeedData,
      ...transformedCmsEventData,
    ])
      .then(() => console.log("Bulk upsert completed successfully."))
      .catch((err) => console.error("Bulk upsert failed:", err));
  } catch (error) {
    console.error("Error fetching CMS Data:", error);
  }
};

const getCMSData = async (channelId, isTotalRequired = true) => {
  try {
    const response = await salesforce.executeAPI(
      `connect/cms/delivery/channels/${channelId}/contents/query`,
      "GET",
      null,
      isTotalRequired,
      false
    );
    return response;
  } catch (error) {
    console.error("Error retrieving data from SF CMS:", error);
    throw error;
  }
};

const updateMappingCountryInstitution = (data) => {
  try {
    return data.flatMap((item) => {
      const countries = item.contentNodes.Applicable_Countries?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const institutions = item.contentNodes.Applicable_Institutions?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const statuses = item.contentNodes.Applicable_Statuses?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");

      return countries.flatMap((country) =>
        institutions.flatMap((institution) =>
          statuses.map((status) => {
            const recId = uuidv4();
            return {
              PK: `CMSFeed#${country}#${institution}#${status}`,
              SK: `${item.contentKey}`,
              Title: item.title,
              Description: item.contentNodes.Description?.value,
              Image_Link__c: item.contentNodes.Image_Link?.value,
              Hyper_Link__c: item.contentNodes.Hyper_Link?.value,
              Published_Date__c: item.publishedDate,
              Is_Active__c: item.contentNodes.Is_Active?.value,
              Applicable_Country__c: country,
              Applicable_Institution__c: institution,
              Applicable_Status__c: status ? parseInt(status, 10) : "",
              Pages__c: item.contentNodes.Pages?.value,
              ContentKey: item.contentKey,
            };
          })
        )
      );
    });
  } catch (error) {
    console.log(
      "Error occurred at data mapping(updateMapCountryInstitution): ",
      error
    );
    throw error;
  }
};

const constructCMSEventObject = (data) => {
  try {
    return data.flatMap((item) => {
      const countries = item.contentNodes.Country?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const institutions = item.contentNodes.Institution?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");

      return countries.flatMap((country) =>
        institutions.flatMap((institution) => {
          return {
            PK: `CMSEvent#${country}#${institution}`,
            SK: `${item.contentKey}`,
            Title: item.contentNodes.Event_Title?.value,
            Description: item.contentNodes.Event_Description?.value,
            Registration_Link__c: item.contentNodes.Registration_Link?.value,
            Thumbnail_Image__c: item.contentNodes.Thumbnail_Image?.value,
            Event_Date_Time__c:
              item.contentNodes.Event_Date_Time?.dateTimeValue,
            Is_Active__c: "yes",
            Applicable_Country__c: country,
            Applicable_Institution__c: institution,
            ContentKey: item.contentKey,
          };
        })
      );
    });
  } catch (error) {
    console.log(
      "Error occurred at data mapping(constructCMSEventObject): ",
      error
    );
    throw error;
  }
};

const bulkUpsert = async (tableName, items) => {
  const transactions = [];
  try {
    const client = new DynamoDBClient({ region: process.env.REGION });
    const tranactionDate = new Date().toISOString();
    for (const item of items) {
      const updateCommand = {
        Update: {
          TableName: tableName,
          Key: {
            PK: { S: item.PK },
            SK: { S: item.SK },
          },
          UpdateExpression:
            "SET Title = :title, Description = :description, Image_Link__c = :imageLink, Hyper_Link__c = :hyperLink, Published_Date__c = :publishedDate, Is_Active__c = :isActive, Applicable_Country__c = :applicableCountry, Applicable_Institution__c = :applicableInstitution, Applicable_Status__c = :applicableStatus, Pages__c = :pages, ContentKey = :contentKey, Updated_Date = :updatedDate, Registration_Link__c = :registrationLink, Thumbnail_Image__c = :thumbnailImage, Event_Date_Time__c = :eventDateTime",
          ExpressionAttributeValues: {
            ":title": { S: item.Title || "" },
            ":description": { S: item.Description || "" },
            ":imageLink": { S: item.Image_Link__c || "" },
            ":hyperLink": { S: item.Hyper_Link__c || "" },
            ":publishedDate": { S: item.Published_Date__c || "" },
            ":isActive": { S: item.Is_Active__c },
            ":applicableCountry": { S: item.Applicable_Country__c || "" },
            ":applicableInstitution": {
              S: item.Applicable_Institution__c || "",
            },
            ":applicableStatus": {
              S:
                item.Applicable_Status__c !== undefined
                  ? item.Applicable_Status__c.toString()
                  : "",
            },
            ":pages": { S: item.Pages__c || "" },
            ":contentKey": { S: item.ContentKey || "" },
            ":updatedDate": { S: tranactionDate || "" },
            ":registrationLink": {
              S: item.Registration_Link__c || "",
            },
            ":thumbnailImage": {
              S: item.Thumbnail_Image__c || "",
            },
            ":eventDateTime": {
              S: item.Event_Date_Time__c || "",
            },
          },
        },
      };
      transactions.push(updateCommand);
    }

    const chunkedTransactions = [];
    for (let i = 0; i < transactions.length; i += 25) {
      chunkedTransactions.push(transactions.slice(i, i + 25));
    }

    for (const chunk of chunkedTransactions) {
      const command = new TransactWriteItemsCommand({ TransactItems: chunk });
      await client.send(command);
    }
  } catch (error) {
    console.log("Error occued at dynamodb write:", error);
    throw error;
  }
};
