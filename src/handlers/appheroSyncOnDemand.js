const AWS = require("aws-sdk");
const salesforce = require("../common/salesforce.service");
const { LoggerService } = require("../common/logger.service");
const { ErrorAccumulator } = require("../common/error-accumulator");
const { ErrorReporter } = require("../common/error-reporter");
const {
  SYNC_EVENTS,
  SYNC_OPERATIONS,
  ENTITY_TYPES,
} = require("../constants/syncEvents");
const { v4: uuidv4 } = require("uuid");

module.exports.handler = async (event) => {
  const requestId = uuidv4();
  const logger = new LoggerService();
  const errorAccumulator = new ErrorAccumulator();
  const errorReporter = new ErrorReporter(
    requestId,
    SYNC_OPERATIONS.SYNC_ON_DEMAND
  );

  // Log sync initiation
  await logger.logSync(
    SYNC_EVENTS.ON_DEMAND_SYNC_INITIATED,
    event,
    {},
    "On-demand sync process initiated",
    requestId,
    "",
    "",
    null,
    SYNC_OPERATIONS.SYNC_ON_DEMAND
  );

  console.log(`On-demand sync started with request ID: ${requestId}`);
  const dynamodb = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });
  let emails, recordIds, salesforceQuery;
  const onDemandQueries = [
    {
      entity: "Account",
      tableName: process.env.APPHERO_SF_ACCOUNTS_TABLE,
      projectionExpression: "#pk,#sk,#recordTypeId,#id",
      expressionAttributeNames: {
        "#pk": "PK",
        "#sk": "SK",
        "#recordTypeId": "RecordTypeId",
        "#id": "Id",
      },
    },
    // {
    //   entity: "Task",
    //   tableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
    //   updateTableName: process.env.APPHERO_SF_TASK_TABLE,
    //   projectionExpression: "#pk,#sk,#WhatId",
    //   expressionAttributeNames: {
    //     "#pk": "PK",
    //     "#sk": "SK",
    //     "#WhatId": "WhatId",
    //   },
    // },
    // {
    //   entity: "Opportunity",
    //   tableName: process.env.APPHERO_SF_ACCOUNTS_TABLE,
    //   updateTableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
    //   projectionExpression: "#pk,#sk",
    //   expressionAttributeNames: {
    //     "#pk": "PK",
    //     "#sk": "SK",
    //   },
    // },
    // {
    //   entity: "OpportunityFile",
    //   tableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
    //   updateTableName: process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
    //   projectionExpression: "#pk,#sk",
    //   expressionAttributeNames: {
    //     "#pk": "PK",
    //     "#sk": "SK",
    //   },
    // },
    {
      entity: "Application__c",
      tableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
      updateTableName: process.env.APPHERO_SF_APPLICATION_TABLE,
      projectionExpression: "#pk,#sk",
      expressionAttributeNames: {
        "#pk": "PK",
        "#sk": "SK",
      },
    },
    // {
    //   entity: "OpportunityDocumentType__c",
    //   tableName: "apphero-lookup-dev",
    //   projectionExpression: "#pk,#sk",
    //   expressionAttributeNames: {
    //     "#pk": "PK",
    //     "#sk": "SK",
    //   },
    // }
  ];

  try {
    for (const queryConfig of onDemandQueries) {
      const {
        entity,
        tableName,
        projectionExpression,
        expressionAttributeNames,
      } = queryConfig;

      console.log(`Processing entity: ${entity}`);

      // Log entity sync initiation
      await logger.logSync(
        SYNC_EVENTS[
          `${entity.toUpperCase().replace("__C", "")}_SYNC_INITIATED`
        ] || SYNC_EVENTS.SYNC_STARTED,
        { entity, tableName },
        {},
        `${entity} sync initiated`,
        requestId,
        entity,
        "entity",
        null,
        SYNC_OPERATIONS.SYNC_ON_DEMAND
      );

      const currentTime = new Date().toISOString();
      const oneHourAgo = new Date(
        new Date().getTime() - 1.5 * 60 * 60 * 1000
      ).toISOString();

      const scanParams = {
        TableName: tableName,
        ProjectionExpression: projectionExpression,
        ExpressionAttributeNames: expressionAttributeNames,
      };

      let getAccountsIds;
      try {
        getAccountsIds = await getAllAccounts(scanParams);
        errorAccumulator.addSuccess("DynamoDB_Scan", entity, tableName);
      } catch (error) {
        errorAccumulator.addError("DynamoDB_Scan", entity, tableName, error, {
          scanParams,
        });
        continue; // Skip to next entity if scan fails
      }
      if (
        entity === "OpportunityDocumentType__c" &&
        getAccountsIds.Items.length !== 0
      ) {
        try {
          console.log("OpportunityDocumentType__c initiated");
          errorAccumulator.incrementProcessed();

          const filteredAccounts = getAccountsIds.Items.filter(
            (item) => item.PK === "Brand"
          );
          const brands = filteredAccounts.map((item) => item.SK);
          const formattedBrands = brands.map((brand) => `'${brand}'`).join(",");

          salesforceQuery = `
          SELECT Brand__c, Id, DocumentType__c, Required__c, HideInFileUploader__c, ValidationName__c
          FROM OpportunityDocumentType__c
          WHERE Brand__c IN (${formattedBrands}) AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime}
        `;
          const salesforceRecordResponse = await salesforce.executeAPI(
            salesforceQuery,
            "GET"
          );

          // Enhanced logging for OpportunityDocumentType__c Salesforce response
          await logSalesforceResponse(
            salesforceRecordResponse,
            "OpportunityDocumentType__c",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            brands?.length || 0
          );

          await updateDynamoDB(
            tableName,
            "DocumentType#${Brand__c}",
            "Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "OpportunityDocumentType_Sync",
            "OpportunityDocumentType__c",
            `${brands.length}_brands`
          );
          console.log("OpportunityDocumentType__c completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "OpportunityDocumentType_Sync",
            "OpportunityDocumentType__c",
            `${brands?.length || 0}_brands`,
            error,
            {
              brandCount: brands?.length || 0,
              tableName,
            }
          );
          console.error("OpportunityDocumentType__c sync failed:", error);
        }
      }

      if (entity === "Account" && getAccountsIds.Items.length !== 0) {
        try {
          console.log("Account initalized");
          errorAccumulator.incrementProcessed();

          emails = getAccountsIds.Items.map((item) => `'${item.PK}'`);
          recordIds = [
            ...new Set(
              getAccountsIds.Items.filter((item) => item.RecordTypeId).map(
                (item) => `'${item.RecordTypeId}'`
              )
            ),
          ];

          salesforceQuery = `
        SELECT Id,PersonEmail, RecordTypeId, App_Hero_Consent__c, App_Hero_Can_Apply__c,
        FirstName,Location__pc,Name,Phone
        FROM Account
        WHERE Person IN (${emails.join(
          ","
        )}) AND RecordTypeId IN (${recordIds.join(
            ","
          )}) AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime}
      `;

          const salesforceRecordResponse = await compositeQueryRequest(
            salesforceQuery
          );

          // Enhanced logging for Salesforce response with proper error detection
          await logSalesforceResponse(
            salesforceRecordResponse,
            "Account",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            emails?.length || 0
          );

          await updateDynamoDB(
            tableName,
            "PersonEmail",
            "RecordTypeId_Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "Account_Sync",
            "Account",
            `${emails.length}_emails`
          );
          console.log("Account completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "Account_Sync",
            "Account",
            `${emails?.length || 0}_emails`,
            error,
            {
              emailCount: emails?.length || 0,
              recordIdCount: recordIds?.length || 0,
              tableName,
            }
          );
          console.error("Account sync failed:", error);
        }
      }

      if (entity === "Task" && getAccountsIds.Items.length !== 0) {
        try {
          console.log("Tasks initiated");
          errorAccumulator.incrementProcessed();

          const whatIds = [
            ...new Set(getAccountsIds.Items.map((item) => `'${item.SK}'`)),
          ];
          salesforceQuery = `
        SELECT Id,Description,Status,LastModifiedDate,WhatId,Subject,CreatedDate,Duplicate_Task__c,Document_Type__c,Related_Record_Id__c,Related_Record_Name__c,Follow_Up__c FROM Task WHERE WhatId IN (${whatIds.join(
          ","
        )}) and Subject IN ('Review Center Comment','Review Center Comments','Smart Apply Tasks','Institution Comments','UCW FOLLOWUP TASK') and Duplicate_Task__c = false AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime} AND CreatedDate >= ${
            process.env.GET_TASK_FROM_DATE
          }`;

          const salesforceRecordResponse = await compositeQueryRequest(
            salesforceQuery
          );

          // Enhanced logging for Task Salesforce response
          await logSalesforceResponse(
            salesforceRecordResponse,
            "Task",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            whatIds?.length || 0
          );

          await updateDynamoDB(
            queryConfig.updateTableName,
            "WhatId",
            "Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "Task_Sync",
            "Task",
            `${whatIds.length}_whatIds`
          );
          console.log("Tasks completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "Task_Sync",
            "Task",
            `${whatIds?.length || 0}_whatIds`,
            error,
            {
              whatIdCount: whatIds?.length || 0,
              tableName: queryConfig.updateTableName,
            }
          );
          console.error("Task sync failed:", error);
        }
      }

      if (entity === "Opportunity" && getAccountsIds.Items.length !== 0) {
        try {
          console.log("Opportunity initiated");
          errorAccumulator.incrementProcessed();

          const appheroSupportedBrands = await getAppheroSupportedBrands();
          const emails = [
            ...new Set(getAccountsIds.Items.map((item) => `'${item.PK}'`)),
          ];
          salesforceQuery = `select
          Owner.Phone,
          BusinessUnitFilter__c,
          OwnerEmail__c,
          AgentAccount__r.Phone,
          AgentAccount__r.Email__c,
          Admissions_Condition__c,
          BusinessDeveloperName__c,
          ApplicationFormId__c,
          AgentContact__c,
          Account.Phone,
          Account.Name,
          Account.PersonEmail,
          Account.Id,
          Id,
          ApplicationId__c,
          Brand__c,
          CreatedDate,
          toLabel(Brand__c)Institution,
          Product_Intake_Date__c,
          toLabel(Location__c)location,
          CreatedBy.Name,
          StageName,
          Name,
          toLabel(AdmissionsStage__c),
          ApplicationSubmitted__c,
          ApplicationProgress__c,
          RecordType.Name,
          ProgrammeName__c,
          (Select Location__c,Intake_Date__c,Product2.Duration__c,Product2.ProgrammeName__c,Product2.Campus_Days__c from OpportunityLineItems),
          (Select Id,Visa_Application_Status__c,Arrival_Date__c,Opportunity__c,Visa_Application_Date__c,Visa_Interview_Date__c,Visa_Number__c,Visa_Required__c,CreatedDate,Visa_Application_Reference_Number__c,LastModifiedDate from Visa_Application__r),
          Institution_Full_Name__c,
          AgentAccountName__c,
          OwnerName__c,
          Owner.Appointment_Booking_Link__c,
          Agent_Contact__r.Email,
          Agent_Contact__r.Phone,
          Agent_Contact__r.Name,
          DeclarationDate__c,
          Delivery_Mode__c,
          Application_Submitted_Date__c,
          OverallStartDate__c,
          Student_Placement_Status__c,
          ApplicationSource__c,Application_Submission_Source__c,
          Application_Update_Source__c from Opportunity WHERE AccountEmail__c IN (${emails.join(
            ","
          )}) and RecordType.name!='Agent Onboarding' and RecordType.name!='Default' AND ApplicationSource__c NOT IN ('GUS Core','Link Generator','Salesforce','CORE Cross-Sell') and
          BusinessUnitFilter__c IN ${appheroSupportedBrands} AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime} `;
          const salesforceRecordResponse = await compositeQueryRequest(
            salesforceQuery
          );

          // Enhanced logging for Opportunity Salesforce response
          await logSalesforceResponse(
            salesforceRecordResponse,
            "Opportunity",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            emails?.length || 0
          );

          console.log(
            "Opportunity Records length",
            salesforceRecordResponse?.length || 0
          );

          await updateDynamoDB(
            queryConfig.updateTableName,
            "Account.PersonEmail",
            "Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "Opportunity_Sync",
            "Opportunity",
            `${emails.length}_emails`
          );
          console.log("Opportunity completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "Opportunity_Sync",
            "Opportunity",
            `${emails?.length || 0}_emails`,
            error,
            {
              emailCount: emails?.length || 0,
              tableName: queryConfig.updateTableName,
            }
          );
          console.error("Opportunity sync failed:", error);
        }
      }

      if (entity === "OpportunityFile" && getAccountsIds.Items.length !== 0) {
        try {
          console.log("OpportunityFile initiated");
          errorAccumulator.incrementProcessed();

          const opportunityIds = [
            ...new Set(getAccountsIds.Items.map((item) => `'${item.SK}'`)),
          ];
          salesforceQuery = `
          SELECT Id,Name, ApplicationId__c, CreatedDate, DocumentType__c, LetterType__c, Opportunity__c, OriginalValue__c, OwnerId, S3FileName__c, Opportunity__r.brand__c, Opportunity__r.BusinessUnitFilter__c, DocumentSource__c, BucketName__c, Additional_Info__c FROM OpportunityFile__c WHERE Opportunity__c IN (${opportunityIds.join(
            ","
          )}) AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime}
      `;
          const salesforceRecordResponse = await compositeQueryRequest(
            salesforceQuery
          );

          // Enhanced logging for OpportunityFile Salesforce response
          await logSalesforceResponse(
            salesforceRecordResponse,
            "OpportunityFile",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            opportunityIds?.length || 0
          );

          await updateDynamoDB(
            queryConfig.updateTableName,
            "Opportunity__c",
            "Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "OpportunityFile_Sync",
            "OpportunityFile",
            `${opportunityIds.length}_opportunityIds`
          );
          console.log("OpportunityFile completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "OpportunityFile_Sync",
            "OpportunityFile",
            `${opportunityIds?.length || 0}_opportunityIds`,
            error,
            {
              opportunityIdCount: opportunityIds?.length || 0,
              tableName: queryConfig.updateTableName,
            }
          );
          console.error("OpportunityFile sync failed:", error);
        }
      }

      if (entity === "Application__c" && getAccountsIds.Items.length !== 0) {
        try {
          console.log("Application__c initiated");
          errorAccumulator.incrementProcessed();

          const OpportunityB2CIds = [
            ...new Set(getAccountsIds.Items.map((item) => `'${item.SK}'`)),
          ];
          salesforceQuery = `
        SELECT Agent_Contact__r.Email, Agent_Contact__r.Phone,Id, Agent_Contact__r.Name,Opportunity_B2C__c FROM Application__c WHERE Opportunity__c IN (${OpportunityB2CIds.join(
          ","
        )}) AND LastModifiedDate >= ${oneHourAgo} AND LastModifiedDate <= ${currentTime}
      `;
          const salesforceRecordResponse = await compositeQueryRequest(
            salesforceQuery
          );

          // Check if query failed and returned null
          if (salesforceRecordResponse === null) {
            throw new Error(
              `Application__c query failed - likely due to invalid field in query`
            );
          }

          // Enhanced logging for Application__c Salesforce response
          await logSalesforceResponse(
            salesforceRecordResponse,
            "Application__c",
            salesforceQuery,
            logger,
            errorAccumulator,
            requestId,
            OpportunityB2CIds?.length || 0
          );

          await updateDynamoDB(
            queryConfig.updateTableName,
            "Opportunity_B2C__c",
            "Id",
            salesforceRecordResponse
          );

          errorAccumulator.addSuccess(
            "Application_Sync",
            "Application__c",
            `${OpportunityB2CIds.length}_opportunityB2CIds`
          );
          console.log("Application__c completed successfully");
        } catch (error) {
          errorAccumulator.addError(
            "Application_Sync",
            "Application__c",
            `${OpportunityB2CIds?.length || 0}_opportunityB2CIds`,
            error,
            {
              opportunityB2CIdCount: OpportunityB2CIds?.length || 0,
              tableName: queryConfig.updateTableName,
            }
          );
          console.error("Application__c sync failed:", error);
        }
      }
    }
  } catch (error) {
    // Handle any unexpected errors during the main processing
    errorAccumulator.addError("OnDemandSync", "General", "MainProcess", error);
    console.error("Unexpected error during on-demand sync:", error);
  } finally {
    // Generate comprehensive error report using ErrorReporter
    const comprehensiveReport = await errorReporter.generateComprehensiveReport(
      errorAccumulator,
      event,
      "On-Demand Sync"
    );

    // Generate notification content for monitoring systems
    const notificationContent = errorReporter.generateNotificationContent(
      errorAccumulator,
      "On-Demand Sync"
    );

    console.log(`On-demand sync completed. Notification:`, notificationContent);

    // Log detailed Salesforce error summary to console for debugging
    if (errorAccumulator.hasErrors()) {
      console.log("\n🔍 Detailed Salesforce Error Analysis:");
      const errors = errorAccumulator.getErrors();
      const salesforceErrors = errors.filter(
        (error) =>
          error.context &&
          (error.context.salesforceErrorDetails ||
            error.context.detailedErrors ||
            error.context.responseType === "salesforce_error" ||
            error.context.responseType === "composite_error")
      );

      salesforceErrors.forEach((error, index) => {
        console.log(`\nSalesforce Error ${index + 1}:`);
        console.log(`  Operation: ${error.operation}`);
        console.log(`  Entity: ${error.entityType}`);
        console.log(`  Entity ID: ${error.entityId}`);
        console.log(`  Message: ${error.message}`);

        if (error.context.salesforceErrorDetails) {
          const details = error.context.salesforceErrorDetails;
          console.log(`  SF Error Code: ${details.errorCode}`);
          console.log(`  SF Message: ${details.message}`);
          if (details.fields && details.fields.length > 0) {
            console.log(`  SF Fields: ${details.fields.join(", ")}`);
          }
          if (details.line) console.log(`  SF Line: ${details.line}`);
          if (details.column) console.log(`  SF Column: ${details.column}`);
          if (details.compileProblem)
            console.log(`  SF Compile Problem: ${details.compileProblem}`);
          if (details.duplicateResult)
            console.log(
              `  SF Duplicate Result: ${JSON.stringify(
                details.duplicateResult
              )}`
            );
        }

        if (error.context.detailedErrors) {
          console.log(
            `  Composite Errors: ${error.context.detailedErrors.length}`
          );
          error.context.detailedErrors.forEach((compError, compIndex) => {
            console.log(`    Composite Error ${compIndex + 1}:`);
            console.log(`      HTTP Status: ${compError.httpStatusCode}`);
            console.log(`      Reference ID: ${compError.referenceId}`);
            if (compError.salesforceErrorDetails) {
              console.log(
                `      SF Error Code: ${compError.salesforceErrorDetails.errorCode}`
              );
              console.log(
                `      SF Message: ${compError.salesforceErrorDetails.message}`
              );
            }
          });
        }
      });
    }

    // Return appropriate status based on error presence
    return comprehensiveReport.hasErrors
      ? "Completed with errors"
      : "Completed successfully";
  }
};

async function updateDynamoDB(tableName, PKName, SKName, records) {
  if (records) {
    const dynamodb = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });

    let primaryKeyValue;
    for (const record of records) {
      let placeholderPattern = /\${([^}]+)}/;
      if (PKName.includes(".")) {
        primaryKeyValue = getProperty(record, PKName);
      } else if (placeholderPattern.test(PKName)) {
        // primaryKeyValue = record[placeholderPattern.exec(PKName)];
        primaryKeyValue = PKName.replace(
          placeholderPattern,
          (match, placeholder) => {
            return record[placeholder];
          }
        );
      } else {
        primaryKeyValue = record[PKName];
      }
      let sortKeyValue;
      if (SKName.includes("_")) {
        const [recordTypeIdKey, idKey] = SKName.split("_");
        sortKeyValue = `${record[recordTypeIdKey]}_${record[idKey]}`;
      } else {
        sortKeyValue = record[SKName];
      }

      let existingRecord = await getRecordFromDynamoDB(
        tableName,
        primaryKeyValue,
        sortKeyValue
      );

      if (!existingRecord) {
        console.log(
          `Record does not exist in DynamoDB for PK: ${primaryKeyValue} and SK: ${sortKeyValue}`
        );

        try {
          await createRecordInDynamoDB(
            tableName,
            primaryKeyValue,
            sortKeyValue,
            record
          );
        } catch (error) {
          throw new Error("Error while creating record in dynamodb");
        }

        existingRecord = await getRecordFromDynamoDB(
          tableName,
          primaryKeyValue,
          sortKeyValue
        );
      }

      const ExpressionAttributeNames = {};
      const ExpressionAttributeValues = {};
      let UpdateExpression = "SET";

      const updatedAt = new Date().toISOString();
      ExpressionAttributeValues[":updatedAt"] = updatedAt;
      UpdateExpression += ` #updatedAt = :updatedAt,`;
      ExpressionAttributeNames["#updatedAt"] = "updatedAt";

      Object.keys(record).forEach((key, index) => {
        if (key !== PKName) {
          const placeholder = `#field${index}`;
          const valuePlaceholder = `:value${index}`;
          ExpressionAttributeNames[placeholder] = key;
          ExpressionAttributeValues[valuePlaceholder] = record[key];
          UpdateExpression += ` ${placeholder} = ${valuePlaceholder},`;
        }
      });

      UpdateExpression = UpdateExpression.slice(0, -1);

      const params = {
        TableName: tableName,
        Key: { PK: primaryKeyValue, SK: sortKeyValue },
        UpdateExpression,
        ExpressionAttributeNames,
        ExpressionAttributeValues,
      };

      try {
        await dynamodb.update(params).promise();
      } catch (error) {
        throw new Error("Error while updating record in database !!!", error);
      }
    }
  }
}

function getProperty(obj, propPath) {
  const props = propPath.split(".");
  let value = obj;
  for (const prop of props) {
    value = value[prop];
  }

  return value;
}

async function getRecordFromDynamoDB(tableName, primaryKeyValue, sortKeyValue) {
  const dynamodb = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });

  const params = {
    TableName: tableName,
    Key: { PK: primaryKeyValue, SK: sortKeyValue },
  };

  try {
    const data = await dynamodb.get(params).promise();
    return data.Item;
  } catch (error) {
    console.error("Error retrieving record from DynamoDB:", error);
    return null;
  }
}

async function getAppheroSupportedBrands() {
  const params = {
    TableName: process.env.APPHERO_CONSUMER_TABLE,
    Key: {
      ["PK"]: process.env.APPHERO_CONSUMER_KEY,
    },
  };
  const dynamodb = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });
  const consumerDetails = await dynamodb.get(params).promise();
  const brandAccessString = consumerDetails.Item.brandAccess;
  return `(${brandAccessString})`;
}

async function compositeQueryRequest(query) {
  const timestamp = new Date().toISOString();

  if (query.length > 97000) {
    console.log(
      `[${timestamp}] Query too long (${query.length} chars), splitting into batches`
    );
    return await executeQueryInBatches(query);
  }

  const compositeRequest = [
    {
      method: "GET",
      url: `/services/data/v53.0/query/?q=${query}`,
      referenceId: "req",
    },
  ];

  console.log(`[${timestamp}] Executing single composite query...`);

  try {
    const salesforceRecordResponse = await salesforce.compositeRequest(
      compositeRequest
    );

    // Check for composite response errors before returning
    if (salesforceRecordResponse?.compositeResponse?.[0]) {
      const response = salesforceRecordResponse.compositeResponse[0];

      if (response.httpStatusCode >= 400) {
        // Return null to indicate failure, let the calling function handle it
        return null;
      }

      const records = response.body?.records;
      console.log(
        `[${timestamp}] Composite query successful: ${
          records?.length || 0
        } records retrieved`
      );
      return records;
    }

    console.warn(
      `[${timestamp}] Unexpected composite response structure:`,
      salesforceRecordResponse
    );
    return null;
  } catch (error) {
    // Re-throw the error to be handled by the calling function
    throw error;
  }
}

function splitListByCharLength(list, maxLen) {
  console.log("List Length", list.length);

  const result = [];
  let currentList = [];
  let currentLength = 0;

  for (const item of list) {
    if (currentLength + item.length + 1 > maxLen) {
      result.push(currentList);
      currentList = [];
      currentLength = 0;
    }
    currentList.push(item);
    currentLength += item.length + 1;
  }

  if (currentList.length > 0) {
    result.push(currentList);
  }

  return result;
}

async function executeQueryInBatches(query) {
  const inClauseMatch = query.match(/IN\s*\(([^)]+)\)/i);

  if (!inClauseMatch) {
    console.error("Could not find IN clause to split");
    return [];
  }

  const inClauseValues = inClauseMatch[1].split(",").map((val) => val.trim());

  const batches = splitListByCharLength(inClauseValues, 97000);

  console.log(`Split query into ${batches.length} batches`);

  const allPromises = batches.map((batch, index) => {
    const batchValues = batch.join(",");
    const batchQuery = query.replace(inClauseMatch[0], `IN (${batchValues})`);

    console.log(
      `Executing batch ${index + 1}/${batches.length} with ${
        batch.length
      } items`
    );

    return salesforce.compositeRequest([
      {
        method: "GET",
        url: `/services/data/v53.0/query/?q=${batchQuery}`,
        referenceId: `batch_${index + 1}`,
      },
    ]);
  });

  const responses = await Promise.all(allPromises);

  const allRecords = responses.reduce((acc, response, index) => {
    // Enhanced error detection for batch responses
    if (response?.compositeResponse?.[0]?.httpStatusCode >= 400) {
      const errorResponse = response.compositeResponse[0];
      const timestamp = new Date().toISOString();

      console.error(`[${timestamp}] Error in batch ${index + 1}:`, {
        httpStatusCode: errorResponse.httpStatusCode,
        referenceId: errorResponse.referenceId,
        body: errorResponse.body,
        batchIndex: index + 1,
        totalBatches: responses.length,
      });

      // Log detailed error information for this batch
      if (errorResponse.body && Array.isArray(errorResponse.body)) {
        errorResponse.body.forEach((error, errorIndex) => {
          console.error(
            `[${timestamp}] Batch ${index + 1} Error ${errorIndex + 1}:`,
            {
              errorCode: error.errorCode || error.error_code,
              message: error.message || error.error,
              fields: error.fields,
            }
          );
        });
      }

      return acc;
    }

    const batchRecords = response?.compositeResponse?.[0]?.body?.records || [];
    return [...acc, ...batchRecords];
  }, []);

  console.log(`Total records from all batches: ${allRecords.length}`);
  return allRecords;
}

async function createRecordInDynamoDB(
  tableName,
  primaryKeyValue,
  sortKeyValue,
  record
) {
  const dynamodb = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });

  const params = {
    TableName: tableName,
    Item: {
      PK: primaryKeyValue,
      SK: sortKeyValue,
      ...record,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  };

  try {
    await dynamodb.put(params).promise();
    console.log(
      `Record created in DynamoDB for PK: ${primaryKeyValue} and SK: ${sortKeyValue}`
    );
  } catch (error) {
    console.error("Error creating record in DynamoDB:", error);
    throw new Error("Error creating record in DynamoDB)");
  }
}
/**
 * Extract comprehensive Salesforce error details from error response
 * @param {Object} errorResponse - The Salesforce error response
 * @returns {Object} Structured error details
 */
function extractSalesforceErrorDetails(errorResponse) {
  const errorDetails = {
    errorCode:
      errorResponse.errorCode || errorResponse.error_code || "UNKNOWN_ERROR",
    message:
      errorResponse.message ||
      errorResponse.error ||
      "Unknown Salesforce error",
    statusCode: errorResponse.statusCode || errorResponse.status || "UNKNOWN",
    fields: errorResponse.fields || [],

    // Additional Salesforce-specific error details
    duplicateResult: errorResponse.duplicateResult || null,
    duplicateRuleEntityType: errorResponse.duplicateRuleEntityType || null,
    allowSave: errorResponse.allowSave || null,

    // SOQL/Query specific errors
    line: errorResponse.line || null,
    column: errorResponse.column || null,
    compileProblem: errorResponse.compileProblem || null,

    // Validation errors
    validationErrors: errorResponse.validationErrors || [],

    // Rate limiting
    retryAfterSecs: errorResponse.retryAfterSecs || null,

    // Additional context
    sobjectType: errorResponse.sobjectType || null,
    id: errorResponse.id || null,
  };

  // Handle array of errors (common in composite responses)
  if (Array.isArray(errorResponse)) {
    const firstError = errorResponse[0] || {};
    errorDetails.errorCode =
      firstError.errorCode || firstError.error_code || "MULTIPLE_ERRORS";
    errorDetails.message = errorResponse
      .map((err) => err.message || err.error)
      .join("; ");
    errorDetails.fields = errorResponse.flatMap((err) => err.fields || []);
    errorDetails.allErrors = errorResponse; // Keep all errors for detailed analysis
  }

  // Handle nested error structures
  if (errorResponse.body && Array.isArray(errorResponse.body)) {
    const bodyErrors = errorResponse.body;
    errorDetails.errorCode = bodyErrors[0]?.errorCode || "COMPOSITE_ERROR";
    errorDetails.message = bodyErrors.map((err) => err.message).join("; ");
    errorDetails.fields = bodyErrors.flatMap((err) => err.fields || []);
    errorDetails.allErrors = bodyErrors;
  }

  return errorDetails;
}

/**
 * Enhanced logging function for Salesforce API responses
 * Detects and logs both successful responses and various error conditions
 */
async function logSalesforceResponse(
  response,
  entityType,
  query,
  logger,
  errorAccumulator,
  requestId,
  recordCount = 0
) {
  const timestamp = new Date().toISOString();
  const operation = `${entityType}_Salesforce_Query`;

  try {
    // Check if response is null or undefined
    if (!response) {
      const errorMsg = `Salesforce API returned null/undefined response for ${entityType}`;
      console.error(`[${timestamp}] ${errorMsg}`);

      errorAccumulator.addError(
        operation,
        entityType,
        `${recordCount}_records`,
        errorMsg,
        {
          query: query.substring(0, 200) + "...", // Truncate long queries
          recordCount,
          responseType: "null_response",
        }
      );
      return;
    }

    // Check if response is an array (successful query result)
    if (Array.isArray(response)) {
      const successMsg = `Salesforce query successful for ${entityType}: ${response.length} records retrieved`;
      console.log(`[${timestamp}] ${successMsg}`);

      await logger.logSync(
        `${entityType.toUpperCase()}_SYNC_SUCCESS`,
        { query, recordCount },
        { recordsRetrieved: response.length },
        successMsg,
        requestId,
        entityType,
        "entity",
        { recordsRetrieved: response.length },
        "SYNC_ON_DEMAND"
      );

      errorAccumulator.addSuccess(
        operation,
        entityType,
        `${response.length}_records`
      );
      return;
    }

    // Check for Salesforce error response structure
    if (response && typeof response === "object") {
      // Check for direct error structure
      if (response.error || response.errorCode || response.message) {
        const errorDetails = extractSalesforceErrorDetails(response);
        const errorMsg = `Salesforce API error for ${entityType}: ${errorDetails.errorCode} - ${errorDetails.message}`;

        // Enhanced console logging with full Salesforce error details
        console.error(`[${timestamp}] ${errorMsg}`);
        console.error(`[${timestamp}] Salesforce Error Details:`, {
          errorCode: errorDetails.errorCode,
          message: errorDetails.message,
          statusCode: errorDetails.statusCode,
          fields: errorDetails.fields,
          duplicateResult: errorDetails.duplicateResult,
          duplicateRuleEntityType: errorDetails.duplicateRuleEntityType,
          allowSave: errorDetails.allowSave,
          query: query.substring(0, 300) + (query.length > 300 ? "..." : ""),
          fullErrorResponse: response,
        });

        // Only add to error accumulator, don't log to CloudWatch here to avoid duplicate alerts
        errorAccumulator.addError(
          operation,
          entityType,
          `${recordCount}_records`,
          errorMsg,
          {
            salesforceErrorDetails: errorDetails,
            query: query.substring(0, 200) + "...",
            recordCount,
            responseType: "salesforce_error",
            fullErrorResponse: response,
          }
        );
        return;
      }

      // Check for composite response errors
      if (
        response.compositeResponse &&
        Array.isArray(response.compositeResponse)
      ) {
        const errorResponses = response.compositeResponse.filter(
          (resp) => resp.httpStatusCode >= 400 || (resp.body && resp.body.error)
        );

        if (errorResponses.length > 0) {
          // Extract detailed error information from each failed response
          const detailedErrors = errorResponses.map((resp) => {
            const errorDetails = extractSalesforceErrorDetails(
              resp.body || resp
            );
            return {
              httpStatusCode: resp.httpStatusCode,
              referenceId: resp.referenceId,
              salesforceErrorDetails: errorDetails,
              rawBody: resp.body,
            };
          });

          const errorMsg = `Salesforce composite request errors for ${entityType}: ${errorResponses.length} failed requests`;

          // Add to error accumulator for final logging
          errorAccumulator.addError(
            operation,
            entityType,
            `${recordCount}_records`,
            errorMsg,
            {
              detailedErrors,
              query: query.substring(0, 200) + "...",
              recordCount,
              responseType: "composite_error",
              totalFailedRequests: errorResponses.length,
            }
          );
          return;
        }
      }

      // If we reach here, it's an unexpected response structure
      const warningMsg = `Unexpected Salesforce response structure for ${entityType}`;
      console.warn(`[${timestamp}] ${warningMsg}`, {
        responseKeys: Object.keys(response),
        responseType: typeof response,
        query: query.substring(0, 200) + "...",
      });

      errorAccumulator.addWarning(
        operation,
        entityType,
        `${recordCount}_records`,
        warningMsg,
        {
          responseKeys: Object.keys(response),
          responseType: typeof response,
          query: query.substring(0, 200) + "...",
          recordCount,
        }
      );
    }
  } catch (loggingError) {
    // Fallback logging if our enhanced logging fails
    console.error(
      `[${timestamp}] Error in enhanced Salesforce response logging:`,
      loggingError
    );
    console.error(
      `[${timestamp}] Original Salesforce response for ${entityType}:`,
      response
    );

    errorAccumulator.addError(
      "Logging_Error",
      entityType,
      "response_logging",
      `Failed to log Salesforce response: ${loggingError.message}`,
      { originalResponse: response, loggingError: loggingError.message }
    );
  }
}

async function getAllAccounts(params) {
  const dynamodb = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });
  let items = [];
  let lastEvaluatedKey = null;

  do {
    if (lastEvaluatedKey) {
      params.ExclusiveStartKey = lastEvaluatedKey;
    }
    const data = await dynamodb.scan(params).promise();
    items = items.concat(data.Items);

    lastEvaluatedKey = data.LastEvaluatedKey;
  } while (lastEvaluatedKey);

  return { Items: items };
}
