const AWS = require("aws-sdk");
const parquet = require("parquetjs");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");
const { getData, postData } = require("../common/salesforce.service");

const s3 = new AWS.S3();

const schema = new parquet.ParquetSchema({
  correlationid: { type: "UTF8" },
  timestamp: { type: "UTF8" },
  component: { type: "UTF8" },
  source: { type: "UTF8" },
  destination: { type: "UTF8" },
  event: { type: "UTF8" },
  usecase: { type: "UTF8" },
  sourcePayload: { type: "UTF8" },
  destinationPayload: { type: "UTF8" },
  logMessage: { type: "UTF8" },
  brand: { type: "UTF8", optional: true },
  secondaryKey: { type: "UTF8" },
  entityKeyField: { type: "UTF8", optional: true },
  entityKey: { type: "UTF8", optional: true },
  destinationObjectType: { type: "UTF8", optional: true },
  destinationObjectId: { type: "UTF8", optional: true },
  sourceObjectType: { type: "UTF8", optional: true },
  sourceObjectId: { type: "UTF8", optional: true },
  destinationResponse: { type: "UTF8", optional: true },
});

module.exports.handler = async (event) => {
  const bucketName = process.env.APPHERO_LOGS_EXPORTS_BUCKET_NAME;
  const correlationid = uuidv4();
  console.log("correlationid", correlationid);

  try {
    console.log("Event -->", JSON.stringify(event));
    const snsMessage = event.Records[0].Sns.Message;
    const parsedMessage = JSON.parse(snsMessage);

    const headers = parsedMessage.mail.headers;
    const customHeaders = headers.reduce((acc, header) => {
      acc[header.name.toLowerCase()] = header.value;
      return acc;
    }, {});

    console.log("Custom headers ->", customHeaders);
    console.log("X-Source Header:", `"${customHeaders["x-source"]}"`);
    console.log("Subject Header:", `"${customHeaders.subject}"`);
    const xSource = customHeaders["x-source"]?.trim();
    const subject = customHeaders.subject?.trim();

    console.log("X-Source Header (processed):", `"${xSource}"`);
    console.log("Subject Header (processed):", `"${subject}"`);

    if (
      xSource?.toLowerCase() !== "apphero" &&
      xSource?.toLowerCase() !== "post_deposit" &&
      xSource?.toLowerCase() !== "pre_deposit" &&
      (!subject || !subject.trim().toLowerCase().includes("apphero"))
    ) {
      console.log(
        "Skipping processing as X-Source is not 'Apphero' or Subject does not contain 'Apphero'"
      );
      console.log("xSource:", xSource, "subject:", subject);
      return;
    }

    if (
      xSource?.toLowerCase() === "post_deposit" ||
      xSource?.toLowerCase() === "pre_deposit"
    ) {
      console.log("Source", xSource?.toLowerCase());
      await handlePostAndPreDepositEvents(parsedMessage);
    }

    const sanitizedCustomHeaders = Object.keys(customHeaders).reduce(
      (acc, key) => {
        acc[key] =
          typeof customHeaders[key] === "string"
            ? customHeaders[key]
            : String(customHeaders[key]);
        return acc;
      },
      {}
    );

    console.log("sanitizedCustomHeaders ->", sanitizedCustomHeaders);
    const dateObj = new Date();
    const isoTimestamp = dateObj.toISOString();
    const epochTimestamp = dateObj.getTime();
    const tempFilePath = path.join("/tmp", `${epochTimestamp}_data.parquet`);

    const messageDetails = sanitizedCustomHeaders["messagedetails"]
      ? JSON.parse(sanitizedCustomHeaders["messagedetails"])
      : {};
    const brand = messageDetails.brand || null;

    const logObject = {
      correlationid,
      timestamp: isoTimestamp,
      component: "APPHERO_MAILER_EVENTS_TRACKER_LAMBDA",
      source: "APPHERO_MAILER_EVENTS_TRACKER_TOPIC",
      destination: "APPHERO_LOGS_EXPORTS_BUCKET",
      event: "CAPTURE_MAIL_EVENTS",
      usecase: parsedMessage.eventType.toUpperCase(),
      sourcePayload: JSON.stringify(sanitizedCustomHeaders),
      destinationPayload: JSON.stringify(sanitizedCustomHeaders),
      logMessage: `${parsedMessage.eventType} for ${sanitizedCustomHeaders.to}`,
      brand,
      secondaryKey: sanitizedCustomHeaders.to || null,
      entityKeyField: null,
      entityKey: null,
      destinationObjectType: null,
      destinationObjectId: null,
      sourceObjectType: null,
      sourceObjectId: null,
      destinationResponse: null,
    };

    console.log("Log ->", logObject);
    const writer = await parquet.ParquetWriter.openFile(schema, tempFilePath);
    await writer.appendRow(logObject);
    await writer.close();

    const params = {
      Bucket: bucketName,
      Key: `logs/${epochTimestamp}_data.parquet`,
      Body: fs.createReadStream(tempFilePath),
    };

    await s3.upload(params).promise();

    console.log("Parquet file uploaded to S3");
  } catch (error) {
    console.error("Error processing event or uploading to S3:", error);
  }
};

const handlePostAndPreDepositEvents = async (event) => {
  console.log("Event in post and pre deposit", event);

  const emailMessage = await getData(
    `gus/emailmessage/${event?.mail?.messageId}`,
    event?.mail?.messageId,
    process.env.GUS_MIDDLEWARE_API_KEY
  );

  console.log("Email message ->", emailMessage);

  let status = event.eventType;

  const updateEmailMessage = await postData(
    `gus/emailmessage/${emailMessage?.Id}`,
    {
      Email_Status__c: status,
    },
    event?.mail?.messageId,
    process.env.GUS_MIDDLEWARE_API_KEY,
    "PATCH"
  );

  console.log("Updated Email Message", updateEmailMessage);
};
