const AWS = require("aws-sdk");
const parquet = require("parquetjs");
const zlib = require("zlib");
const path = require("path");
const fs = require("fs");
const s3 = new AWS.S3();

const apphero_logs_schema = new parquet.ParquetSchema({
  correlationId: { type: "UTF8" },
  timestamp: { type: "UTF8" },
  type: { type: "UTF8" },
  component: { type: "UTF8" },
  source: { type: "UTF8" },
  destination: { type: "UTF8" },
  event: { type: "UTF8" },
  usecase: { type: "UTF8", optional: true },
  sourcePayload: { type: "JSON", optional: true },
  destinationPayload: { type: "JSON", optional: true },
  logMessage: { type: "UTF8", optional: true },
  errorMessage: { type: "UTF8", optional: true },
  brand: { type: "UTF8", optional: true },
  secondaryKey: { type: "UTF8", optional: true },
  entityKeyField: { type: "UTF8", optional: true },
  entityKey: { type: "UTF8", optional: true },
  destinationObjectType: { type: "UTF8", optional: true },
  destinationObjectId: { type: "UTF8", optional: true },
  sourceObjectType: { type: "UTF8", optional: true },
  sourceObjectId: { type: "UTF8", optional: true },
  destinationResponse: { type: "UTF8", optional: true },
  version: { type: "UTF8", optional: true },
});

module.exports.handler = async (event) => {
  try {
    const bufferData = Buffer.from(event.awslogs.data, "base64");
    const dateObj = new Date();
    const epochTimestamp = dateObj.getTime();
    const decompressedData = zlib.unzipSync(bufferData).toString("utf-8");
    console.log("decompressedData", decompressedData);
    const logEvents = JSON.parse(decompressedData);
    const logGroup = logEvents.logGroup;

    console.log("Log group ->", logGroup);
    const schema = apphero_logs_schema;
    const bucketName = process.env.APPHERO_LOGS_EXPORTS_BUCKET_NAME;

    const tempFilePath = path.join("/tmp", `${epochTimestamp}_data.parquet`);

    const writer = await parquet.ParquetWriter.openFile(schema, tempFilePath);
    await Promise.all(
      logEvents.logEvents.map(async (logEvent) => {
        const messageStartIndex = logEvent.message.indexOf("{");
        const jsonMessage = logEvent.message.slice(messageStartIndex);
        let message = JSON.parse(jsonMessage);

        if (Array.isArray(message.sourcePayload)) {
          message.sourcePayload = JSON.stringify(message.sourcePayload);
        }

        if (Array.isArray(message.destinationPayload)) {
          message.destinationPayload = JSON.stringify(
            message.destinationPayload
          );
        }

        console.log("Message ->", message);
        if (message.destinationResponse) {
          message.destinationResponse = JSON.stringify(
            message.destinationResponse
          );
        }

        console.log("Message:", message);
        await writer.appendRow(message);
      })
    );
    await writer.close();

    const params = {
      Bucket: bucketName,
      Key: `logs/${epochTimestamp}_data.parquet`,
      Body: fs.createReadStream(tempFilePath),
    };

    await s3.upload(params).promise();

    console.log("Parquet file uploaded to S3");
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};
