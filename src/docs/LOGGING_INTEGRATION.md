# Logging Integration for AppHero SF Sync Service

This document describes the logging integration implemented in the apphero-sf-sync-service, following the same patterns used in the apphero-backend-service.

## Overview

The logging integration provides:
- **Fault-tolerant sync processing** - Errors for individual objects don't stop the entire sync
- **Comprehensive error accumulation** - All errors are collected and reported together
- **Detailed error reporting** - Specific information about what failed and why
- **Reusable logging services** - Can be used across all sync handlers

## Architecture

### Core Components

1. **LoggerService** (`src/common/logger.service.js`)
   - Wraps the `@gus-eip/loggers` CloudWatchLoggerService
   - Provides simplified methods for sync operations
   - Handles log stream naming and component identification

2. **ErrorAccumulator** (`src/common/error-accumulator.js`)
   - Collects errors without stopping execution
   - Tracks success/failure statistics
   - Provides batch processing with error handling
   - Generates error summaries and reports

3. **ErrorReporter** (`src/common/error-reporter.js`)
   - Creates comprehensive error reports
   - Generates actionable recommendations
   - Provides notification content for monitoring
   - Logs detailed error summaries by entity type

4. **Sync Event Constants** (`src/constants/syncEvents.js`)
   - Defines standardized event types
   - Provides entity types and operation constants
   - Ensures consistent logging across the service

## Usage Examples

### Basic Logging

```javascript
const { LoggerService } = require("../common/logger.service");
const { SYNC_EVENTS, SYNC_OPERATIONS } = require("../constants/syncEvents");

const logger = new LoggerService();
const requestId = uuidv4();

// Log successful operation
await logger.logSync(
  SYNC_EVENTS.ACCOUNT_SYNC_COMPLETED,
  sourceData,
  resultData,
  "Account sync completed successfully",
  requestId,
  "account_email",
  "email",
  resultData,
  SYNC_OPERATIONS.SYNC_ON_DEMAND
);

// Log error
await logger.errorSync(
  SYNC_EVENTS.ACCOUNT_SYNC_FAILED,
  sourceData,
  error,
  `Account sync failed: ${error.message}`,
  requestId,
  "account_email",
  "email",
  error,
  SYNC_OPERATIONS.SYNC_ON_DEMAND
);
```

### Error Accumulation

```javascript
const { ErrorAccumulator } = require("../common/error-accumulator");

const errorAccumulator = new ErrorAccumulator();

// Process items with fault tolerance
for (const item of items) {
  try {
    const result = await processItem(item);
    errorAccumulator.addSuccess("Item_Processing", "Item", item.id);
  } catch (error) {
    errorAccumulator.addError("Item_Processing", "Item", item.id, error, { item });
    // Continue processing other items
  }
}

// Check results
const stats = errorAccumulator.getStats();
console.log(`Processed: ${stats.processedCount}, Success Rate: ${stats.successRate}%`);
```

### Comprehensive Error Reporting

```javascript
const { ErrorReporter } = require("../common/error-reporter");

const errorReporter = new ErrorReporter(requestId, SYNC_OPERATIONS.SYNC_ON_DEMAND);

// Generate comprehensive report
const report = await errorReporter.generateComprehensiveReport(
  errorAccumulator,
  originalEvent,
  "Account Sync Process"
);

// Generate notification content
const notification = errorReporter.generateNotificationContent(
  errorAccumulator,
  "Account Sync Process"
);

console.log("Notification Subject:", notification.subject);
console.log("Recommendations:", report.recommendations);
```

### Batch Processing with Error Handling

```javascript
const items = [/* your items */];

const results = await errorAccumulator.processItemsWithErrorHandling(
  items,
  async (item) => await processItem(item), // Processing function
  "Batch_Processing", // Operation name
  (item) => ({ entityType: "Item", entityId: item.id }), // Entity info extractor
  { batchSize: 10, maxConcurrency: 5 } // Options
);

// Results array contains processed items or null for failed items
const successfulResults = results.filter(r => r !== null);
```

## Integration in Sync Handlers

### On-Demand Sync Handler

The `appheroSyncOnDemand.js` handler has been updated to:
- Initialize logging services at the start
- Wrap entity processing in try-catch blocks
- Accumulate errors instead of throwing immediately
- Generate comprehensive reports at the end

### CMS Content Sync Handler

The `appheroSyncCMSContent.js` handler has been updated to:
- Log CMS data fetch operations
- Handle transformation errors gracefully
- Report bulk upsert results
- Provide detailed error analysis

## Environment Variables

The following environment variables are required:

```json
{
  "REGION": "eu-west-1",
  "LOGGER_LOG_GROUP_NAME": "apphero-sf-sync-service-${stage}",
  "TEAMS_WEBHOOK_URL": ""
}
```

## Error Types and Recommendations

The ErrorReporter automatically generates recommendations based on error patterns:

- **Network/Timeout Errors**: Suggests increasing timeouts and checking connectivity
- **Authentication Errors**: Recommends verifying credentials and permissions
- **Rate Limiting**: Suggests implementing backoff strategies
- **Validation Errors**: Recommends reviewing data quality
- **High Error Counts**: Suggests batch size optimization

## Testing

Run the logging integration test:

```bash
node src/test/logging-integration-test.js
```

This test demonstrates:
- Basic logging functionality
- Error accumulation
- Comprehensive reporting
- Notification generation
- Batch processing with error handling

## Benefits

1. **Fault Tolerance**: Individual object failures don't stop the entire sync
2. **Comprehensive Monitoring**: All errors are logged with detailed context
3. **Actionable Insights**: Automatic recommendations for common issues
4. **Consistent Logging**: Standardized event types and formats
5. **Performance Tracking**: Success rates and processing statistics
6. **Easy Debugging**: Request IDs and detailed error context

## Best Practices

1. **Always use unique request IDs** for tracking operations
2. **Accumulate errors** instead of throwing immediately in batch operations
3. **Log both successes and failures** for complete visibility
4. **Use appropriate event types** from the constants file
5. **Include relevant context** in error reports
6. **Generate comprehensive reports** at the end of operations
7. **Monitor success rates** and set up alerts for low rates

## Migration Notes

- The logging integration is backward compatible
- Existing console.log statements remain functional
- New structured logging provides additional insights
- Error handling is now more robust and informative
