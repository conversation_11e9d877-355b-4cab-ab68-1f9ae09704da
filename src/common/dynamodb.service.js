const AWS = require("aws-sdk");

class DynamoDBService {
  async putObject(table, object) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params = {
      TableName: table,
      Item: object.Item,
    };
    return await dynamoDB.put(params).promise();
  }

  async updateObject(table, key, object) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const { keys, update } = this.dynamodbUpdateRequest({
      keys: key,
      values: object,
    });
    const params = {
      TableName: table,
      Key: keys,
      ...update,
    };
    return await dynamoDB.update(params).promise();
  }

  async getObject(
    table,
    keys,
    projectionExpression = null,
    expressionAttributeNames = null,
  ) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params = {
      TableName: table,
      Key: keys,
      ...projectionExpression,
      ...expressionAttributeNames,
    };
    return await dynamoDB.get(params).promise();
  }

  async queryObjects(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.query(params).promise();
  }

  async batchGet(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.batchGet(params).promise();
  }

  async deleteObject(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.delete(params).promise();
  }

  dynamodbUpdateRequest(params) {
    const { keys, values } = params;
    const sets = [];
    const removes = [];
    const expressionNames = {};
    const expValues = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;

      if (value !== undefined && value !== null) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(', ')}` : '';
    if (removes.length) {
      expression += `${expression ? ' ' : ''}REMOVE ${removes.join(', ')}`;
    }

    const updateParams = {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
      },
    };
    if (Object.keys(expValues).length > 0) {
      updateParams.update['ExpressionAttributeValues'] = expValues;
    }

    return updateParams;
  }

  async uploadBulkDataToDynamoDB(json, tableName) {
    const chunkSize = 25;
    const iterations = Math.ceil(json.length / chunkSize);
    console.log(iterations);
    for (let i = 0; i < iterations; i++) {
      const start = i * chunkSize;
      const chunk = json.slice(start, start + chunkSize);
      await this.uploadBatch(chunk, tableName);
      console.log('Chunk:', chunk.length);
    }
  }

  async uploadBatch(items, tableName) {
    try {
      const dynamoDBService = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const batchRequests = items.map((item) => ({
        PutRequest: {
          Item: item,
        },
      }));

      const params = {
        RequestItems: {
          [tableName]: batchRequests,
        },
      };
      const result = await dynamoDBService.batchWrite(params).promise();
      console.log('result', result);
      return result;
    } catch (error) {
      console.error('Error uploading batch to DynamoDB:', error);
      throw error;
    }
  }

  async getAppheroSupportedBrand() {
    const consumerDetails = await this.getObject(
      process.env.CONSUMER_CONFIG_TABLE,
      {
        PK: process.env.GUS_MIDDLEWARE_API_KEY,
      },
    );
    const brandAccessString = consumerDetails.Item.brandAccess;
    return brandAccessString
      .split(',')
      .map((item) => item.replace(/(^'|'$)/g, '').trim());
  }
}

module.exports = new DynamoDBService();