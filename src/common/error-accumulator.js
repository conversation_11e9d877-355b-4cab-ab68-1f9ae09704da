/**
 * Error Accumulator Utility for apphero-sf-sync-service
 * Collects and accumulates errors during sync processing without stopping execution
 * Provides fault tolerance by allowing processing to continue even when individual operations fail
 */
class ErrorAccumulator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.processedCount = 0;
    this.successCount = 0;
    this.failureCount = 0;
  }

  /**
   * Add an error to the accumulator
   * @param {string} operation - The operation that failed
   * @param {string} entityType - Type of entity being processed (e.g., 'Account', 'Opportunity')
   * @param {string} entityId - ID of the entity that failed
   * @param {string|Error} error - Error message or Error object
   * @param {any} context - Additional context data
   */
  addError(operation, entityType, entityId, error, context = {}) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : null;

    this.errors.push({
      timestamp: new Date().toISOString(),
      operation,
      entityType,
      entityId,
      message: errorMessage,
      stack: errorStack,
      context,
    });

    this.failureCount++;
    console.error(
      `Error in ${operation} for ${entityType} ${entityId}: ${errorMessage}`
    );
  }

  /**
   * Add a warning to the accumulator
   * @param {string} operation - The operation that generated the warning
   * @param {string} entityType - Type of entity being processed
   * @param {string} entityId - ID of the entity
   * @param {string} message - Warning message
   * @param {any} context - Additional context data
   */
  addWarning(operation, entityType, entityId, message, context = {}) {
    this.warnings.push({
      timestamp: new Date().toISOString(),
      operation,
      entityType,
      entityId,
      message,
      context,
    });

    console.warn(
      `Warning in ${operation} for ${entityType} ${entityId}: ${message}`
    );
  }

  /**
   * Record a successful operation
   * @param {string} operation - The operation that succeeded
   * @param {string} entityType - Type of entity processed
   * @param {string} entityId - ID of the entity
   */
  addSuccess(operation, entityType, entityId) {
    this.successCount++;
    console.log(`Success: ${operation} for ${entityType} ${entityId}`);
  }

  /**
   * Increment the processed count
   */
  incrementProcessed() {
    this.processedCount++;
  }

  /**
   * Check if there are any errors
   * @returns {boolean} True if there are errors
   */
  hasErrors() {
    return this.errors.length > 0;
  }

  /**
   * Check if there are any warnings
   * @returns {boolean} True if there are warnings
   */
  hasWarnings() {
    return this.warnings.length > 0;
  }

  /**
   * Get all errors
   * @returns {Array} Array of error objects
   */
  getErrors() {
    return this.errors;
  }

  /**
   * Get all warnings
   * @returns {Array} Array of warning objects
   */
  getWarnings() {
    return this.warnings;
  }

  /**
   * Get processing statistics
   * @returns {Object} Statistics object
   */
  getStats() {
    return {
      processedCount: this.processedCount,
      successCount: this.successCount,
      failureCount: this.failureCount,
      warningCount: this.warnings.length,
      errorCount: this.errors.length,
      successRate:
        this.processedCount > 0
          ? ((this.successCount / this.processedCount) * 100).toFixed(2)
          : 0,
    };
  }

  /**
   * Get a summary of all errors grouped by operation and entity type
   * @returns {Object} Error summary
   */
  getErrorSummary() {
    const summary = {};

    this.errors.forEach((error) => {
      const key = `${error.operation}_${error.entityType}`;
      if (!summary[key]) {
        summary[key] = {
          operation: error.operation,
          entityType: error.entityType,
          count: 0,
          entityIds: [],
          messages: [],
        };
      }

      summary[key].count++;
      summary[key].entityIds.push(error.entityId);
      if (!summary[key].messages.includes(error.message)) {
        summary[key].messages.push(error.message);
      }
    });

    return summary;
  }

  /**
   * Get a comprehensive report of all errors and warnings
   * @returns {Object} Comprehensive report
   */
  getComprehensiveReport() {
    return {
      summary: this.getStats(),
      errorSummary: this.getErrorSummary(),
      errors: this.errors,
      warnings: this.warnings,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Clear all accumulated errors and warnings
   */
  clear() {
    this.errors = [];
    this.warnings = [];
    this.processedCount = 0;
    this.successCount = 0;
    this.failureCount = 0;
  }

  /**
   * Execute a function with error handling and accumulation
   * @param {Function} fn - Function to execute
   * @param {string} operation - Operation name
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @param {any} context - Additional context
   * @returns {Promise<any>} Result of the function or null if error
   */
  async executeWithErrorHandling(
    fn,
    operation,
    entityType,
    entityId,
    context = {}
  ) {
    try {
      this.incrementProcessed();
      const result = await fn();
      this.addSuccess(operation, entityType, entityId);
      return result;
    } catch (error) {
      this.addError(operation, entityType, entityId, error, context);
      return null;
    }
  }

  /**
   * Process an array of items with parallel execution and error accumulation
   * @param {Array} items - Items to process
   * @param {Function} processFn - Function to process each item
   * @param {string} operation - Operation name
   * @param {Function} getEntityInfo - Function to extract entity type and ID from item
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} Array of results (null for failed items)
   */
  async processItemsWithErrorHandling(
    items,
    processFn,
    operation,
    getEntityInfo,
    options = {}
  ) {
    const { batchSize = 10, maxConcurrency = 5 } = options;

    const results = [];

    // Process items in batches to control concurrency
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);

      const batchPromises = batch.map(async (item, index) => {
        const { entityType, entityId } = getEntityInfo(item);

        return this.executeWithErrorHandling(
          () => processFn(item),
          operation,
          entityType,
          entityId,
          { batchIndex: Math.floor(i / batchSize), itemIndex: index }
        );
      });

      // Limit concurrency within each batch
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }
}

module.exports = { ErrorAccumulator };
