const { CloudWatchLoggerService, LoggerEnum } = require("@gus-eip/loggers");
const { v4: uuidv4 } = require("uuid");

/**
 * Logger service for apphero-sf-sync-service
 * Wraps CloudWatchLoggerService with sync-service specific configurations
 * Based on the pattern from apphero-backend-service
 */
class LoggerService {
  constructor() {
    this.cloudWatchLoggerService = new CloudWatchLoggerService(
      process.env.REGION,
      process.env.LOGGER_LOG_GROUP_NAME,
      process.env.TEAMS_WEBHOOK_URL,
      true
    );
    this.loggerEnum = new LoggerEnum();
  }

  /**
   * Log a successful operation
   * @param {string} requestId - Unique request identifier
   * @param {string} timestamp - ISO timestamp
   * @param {string} component - Source component
   * @param {string} source - Source system
   * @param {string} destination - Destination system
   * @param {string} event - Event type
   * @param {string} usecase - Use case identifier
   * @param {any} sourcePayload - Source payload data
   * @param {any} destinationPayload - Destination payload data
   * @param {string} logMessage - Log message
   * @param {string} brand - Brand identifier
   * @param {string} secondaryKey - Secondary key for log stream
   * @param {string} entityKeyField - Entity key field name
   * @param {string} entityKey - Entity key value
   * @param {string} destinationObjectType - Destination object type
   * @param {string} destinationObjectId - Destination object ID
   * @param {string} sourceObjectType - Source object type
   * @param {string} sourceObjectId - Source object ID
   * @param {any} destinationResponse - Destination response
   */
  async log(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType,
    destinationObjectId,
    sourceObjectType,
    sourceObjectId,
    destinationResponse
  ) {
    const logStream = secondaryKey
      ? `apphero-sf-sync-service/${secondaryKey}/${requestId}`
      : `apphero-sf-sync-service/${requestId}`;

    await this.cloudWatchLoggerService.log(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      logStream,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse
    );
  }

  /**
   * Log an error operation
   * @param {string} requestId - Unique request identifier
   * @param {string} timestamp - ISO timestamp
   * @param {string} component - Source component
   * @param {string} source - Source system
   * @param {string} destination - Destination system
   * @param {string} event - Event type
   * @param {string} usecase - Use case identifier
   * @param {any} sourcePayload - Source payload data
   * @param {any} destinationPayload - Destination payload data
   * @param {string} errorMessage - Error message
   * @param {string} brand - Brand identifier
   * @param {string} secondaryKey - Secondary key for log stream
   * @param {string} entityKeyField - Entity key field name
   * @param {string} entityKey - Entity key value
   * @param {string} destinationObjectType - Destination object type
   * @param {string} destinationObjectId - Destination object ID
   * @param {string} sourceObjectType - Source object type
   * @param {string} sourceObjectId - Source object ID
   * @param {any} destinationResponse - Destination response
   */
  async error(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    errorMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType,
    destinationObjectId,
    sourceObjectType,
    sourceObjectId,
    destinationResponse
  ) {
    const logStream = secondaryKey
      ? `apphero-sf-sync-service/${secondaryKey}/${requestId}`
      : `apphero-sf-sync-service/${requestId}`;

    await this.cloudWatchLoggerService.error(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      errorMessage,
      brand,
      secondaryKey,
      logStream,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse
    );
  }

  /**
   * Simplified log method for sync operations
   * @param {string} event - Event type
   * @param {any} sourcePayload - Source payload
   * @param {any} destinationPayload - Destination payload
   * @param {string} logMessage - Log message
   * @param {string} requestId - Request ID (optional, will generate if not provided)
   * @param {string} entityKey - Entity key (optional)
   * @param {string} entityKeyField - Entity key field (optional)
   * @param {any} response - Response data (optional)
   * @param {string} usecase - Use case (optional)
   */
  async logSync(
    event,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId,
    entityKey,
    entityKeyField,
    response,
    usecase
  ) {
    await this.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      this.loggerEnum.Component.APPHERO_SF_SYNC_SERVICE,
      this.loggerEnum.Component.APPHERO_SF_SYNC_SERVICE,
      this.loggerEnum.Component.SALESFORCE,
      event,
      usecase || "",
      sourcePayload,
      destinationPayload,
      logMessage,
      "APPHERO",
      entityKey || "",
      entityKeyField || "",
      entityKey || "",
      "",
      "",
      "",
      "",
      response
    );
  }

  /**
   * Simplified error method for sync operations
   * @param {string} event - Event type
   * @param {any} sourcePayload - Source payload
   * @param {any} destinationPayload - Destination payload
   * @param {string} errorMessage - Error message
   * @param {string} requestId - Request ID (optional, will generate if not provided)
   * @param {string} entityKey - Entity key (optional)
   * @param {string} entityKeyField - Entity key field (optional)
   * @param {any} response - Response data (optional)
   * @param {string} usecase - Use case (optional)
   */
  async errorSync(
    event,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId,
    entityKey,
    entityKeyField,
    response,
    usecase
  ) {
    await this.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      this.loggerEnum.Component.APPHERO_SF_SYNC_SERVICE,
      this.loggerEnum.Component.APPHERO_SF_SYNC_SERVICE,
      this.loggerEnum.Component.SALESFORCE,
      event,
      usecase || "",
      sourcePayload,
      destinationPayload,
      errorMessage,
      "APPHERO",
      entityKey || "",
      entityKeyField || "",
      entityKey || "",
      "",
      "",
      "",
      "",
      response
    );
  }

  /**
   * Get logger enum for event types and components
   * @returns {LoggerEnum} Logger enum instance
   */
  getLoggerEnum() {
    return this.loggerEnum;
  }
}

module.exports = { LoggerService };
