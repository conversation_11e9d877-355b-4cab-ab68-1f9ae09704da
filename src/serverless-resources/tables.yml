Resources:
  CMSSyncTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-cms-content-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: SKIndex
          KeySchema:
            - AttributeName: SK
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}