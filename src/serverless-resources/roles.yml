Resources:
  appheroSyncServiceAccess:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "lambda.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      RoleName: apphero-sync-access-${sls:stage}
      Policies:
        - PolicyName: apphero-sync-access-${sls:stage}
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:UpdateItem
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:DeleteItem
                  - secretsmanager:GetSecretValue
                  - secretsmanager:UpdateSecret
                  - s3:PutObject
                  - s3:PutObjectAcl
                  - s3:PutObjectTagging
                  - s3:PutBucketPolicy
                  - s3:PutBucketAcl
                  - s3:PutBucketVersioning
                  - s3:PutBucketWebsite
                  - s3:PutReplicationConfiguration
                  - s3:PutBucketLogging
                  - s3:PutBucketLifecycleConfiguration
                  - s3:PutBucketTagging
                  - s3:PutBucketCors
                  - s3:PutBucketObjectLockConfiguration
                  - s3:PutStorageLensConfiguration
                  - s3:PutBucketEncryption
                  - s3:UploadPart
                  - s3:CompleteMultipartUpload
                  - s3:PutObjectVersionAcl
                  - cloudwatch:*
                Resource:
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-account-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-notifications-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/gus-eip-consumer-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-opportunity-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-opportunityfile-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-task-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-application-${sls:stage}
                  - arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/apphero-sf-cms-content-${sls:stage}
                  - arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/lambda/apphero-sync-ondemand-${sls:stage}:*
                  - arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/lambda/apphero-sync-prefill-${sls:stage}:*
                  - arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/lambda/apphero-sync-cmscontent-${sls:stage}:*
                  - arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/lambda/apphero-notifications-processor-${sls:stage}:*
                  - arn:aws:secretsmanager:eu-west-1:${aws:accountId}:secret:salesforce-gus-${sls:stage}-access-token-o6riI3
                  - arn:aws:secretsmanager:${aws:region}:${aws:accountId}:secret:salesforce-gus-${sls:stage}-access-token
                  - "*"
