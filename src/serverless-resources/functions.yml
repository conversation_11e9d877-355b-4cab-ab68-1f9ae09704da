apphero-sync-prefill:
  name: apphero-sync-prefill-${self:provider.stage}
  handler: src/handlers/appheroSyncPrefill.handler
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}

apphero-sync-ondemand:
  name: apphero-sync-ondemand-${self:provider.stage}
  handler: src/handlers/appheroSyncOnDemand.handler
  timeout: 360
  events:
    - schedule: ${self:custom.scheduleRate.${self:provider.stage}}
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}

apphero-sync-cmscontent:
  name: apphero-sync-cmscontent-${self:provider.stage}
  handler: src/handlers/appheroSyncCMSContent.handler
  timeout: 900
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}

apphero-logs-exports:
  name: apphero-logs-exports-${self:provider.stage}
  handler: src/handlers/appheroLogsExports.handler
  timeout: 900
  events:
    - cloudwatchLog: apphero-logs-${self:provider.stage}
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}

apphero-mailer-events-tracker:
  name: apphero-mailer-events-tracker-${self:provider.stage}
  handler: src/handlers/appheroMailerEvents.handler
  timeout: 900
  events:
    - sns: arn:aws:sns:${aws:region}:${aws:accountId}:apphero-mailer-events-tracker-${self:provider.stage}
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}

apphero-notifications-processor:
  name: apphero-notifications-processor-${self:provider.stage}
  handler: src/handlers/appheroNotificationsProcessor.handler
  timeout: 300
  events:
    - schedule: rate(1 hour)
  role: !Sub arn:aws:iam::${aws:accountId}:role/apphero-sync-access-${self:provider.stage}
