import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProfileModule } from './profile/module';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from './common/module';
import { LookUpModule } from './lookup/module';
import { OpportunityModule } from './opportunity/module';
import { ProgrammeModule } from './programme/module';
import { AccountModule } from './account/module';
import { NotificationModule } from './notification/module';
import { CobrandingModule } from './cobranding/module';
import { ChatModule } from './chat/module';
import { CmsContentModule } from './cms-content/cms-content.module';
import { VisaApplicationModule } from './visa-application/module';
import { CaseModule } from './case/module';
import { OAuthModule } from './oauth/oauth.module';
import { updateByObjectModule } from './update-by-object/module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    ProfileModule,
    CommonModule,
    LookUpModule,
    OpportunityModule,
    ProgrammeModule,
    AccountModule,
    NotificationModule,
    CobrandingModule,
    ChatModule,
    CaseModule,
    CmsContentModule,
    VisaApplicationModule,
    OAuthModule,
    updateByObjectModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
