import { Injectable } from '@nestjs/common';
import {
  OAuthParameterStoreService,
  OAuthTokenResponse,
} from '../common/oauth-parameter-store.service';

@Injectable()
export class OAuthService {
  constructor(
    private readonly oauthParameterStoreService: OAuthParameterStoreService,
  ) {}

  /**
   * Generate OAuth access token
   * @returns Promise<OAuthTokenResponse> - OAuth token response
   */
  async generateAccessToken(brand: string): Promise<OAuthTokenResponse> {
    try {
      console.log(`Generating OAuth token`);

      const tokenResponse =
        await this.oauthParameterStoreService.generateAccessToken(brand);

      console.log(`Successfully generated OAuth token`);
      return tokenResponse;
    } catch (error) {
      console.error(`Failed to generate OAuth token:`, error);
      throw new Error(`OAuth token generation failed: ${error.message}`);
    }
  }

  /**
   * Validate credentials access
   * @returns Promise<boolean> - True if credentials are accessible
   */
  async validateCredentialsAccess(brand?: string): Promise<boolean> {
    try {
      return await this.oauthParameterStoreService.validateCredentialsAccess(
        brand,
      );
    } catch (error) {
      console.error(`Failed to validate credentials:`, error);
      return false;
    }
  }

  /**
   * Get parameter name
   * @returns string - Parameter Store path
   */
  getParameterName(): string {
    try {
      return this.oauthParameterStoreService.getCredentialsParameterPath();
    } catch (error) {
      console.error(`Failed to get parameter name:`, error);
      throw new Error(`Failed to get parameter name: ${error.message}`);
    }
  }

  /**
   * Get OAuth endpoint
   * @returns string - OAuth endpoint URL
   */
  getOAuthEndpoint(): string {
    try {
      return this.oauthParameterStoreService.getOAuthEndpoint();
    } catch (error) {
      console.error(`Failed to get OAuth endpoint:`, error);
      throw new Error(`Failed to get OAuth endpoint: ${error.message}`);
    }
  }

  /**
   * Get OAuth credentials
   * @returns Promise<string> - Base64 encoded credentials
   */
  async getOAuthCredentials(brand: string): Promise<string> {
    try {
      return await this.oauthParameterStoreService.getSecret(brand);
    } catch (error) {
      console.error(`Failed to get OAuth credentials:`, error);
      throw new Error(`Failed to get OAuth credentials: ${error.message}`);
    }
  }
}
