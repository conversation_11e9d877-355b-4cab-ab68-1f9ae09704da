import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { NotificationEvent } from '../enums/NotificationEventType';
import { INotificationContent } from './INotificationContent';
import { EmailTemplate } from '../enums/EmailTemplate';
import { AdmissionStatusUpdateContent } from './notificationEventContent/admissionUpdateNotification';
import { EventAdditionalDetail } from '../enums/EventAdditionalDetail';
import { DynamoDBService } from 'apps/src/common/dynamodb.service';
import { AuthService } from 'apps/src/common/auth.service';
import {
  NotificationEventDetail,
  NotificationEventDetails,
} from '../enums/NotificationEventDetail';
import { WelcomeStudentContent } from './notificationEventContent/welcomeStudent';
import { OptimizedNotificationContentBuilder } from '../optimized-notification-content-builder';
import { ReviewCenterContent } from './notificationEventContent/reviewCenterComment';
import { AdmissionConditionContent } from './notificationEventContent/admissionCondition';
import { IssuedLetterContent } from './notificationEventContent/issuedLetter';
import { AgentUploadDocumentContent } from './notificationEventContent/agentUploadDocument';
import { ClosedTask } from './notificationEventContent/closedTask';
import { SalesforceService } from 'apps/src/common/salesforce.service';
import { NotificationInput } from '../enums/NotificationEventInput';
import { OpportunityService } from 'apps/src/opportunity/service';
import { S3Service } from 'apps/src/common/s3.service';
import { CobrandingService } from 'apps/src/cobranding/service';
import { JoinAppheroContent } from './notificationEventContent/joinApphero';
import { TrackNewApplicationContent } from './notificationEventContent/trackNewApplication';
import { AdmissionLetterDeletion } from './notificationEventContent/admissionLetterDeletion';
import { TaskReopen } from './notificationEventContent/taskReopen';
import { OpportunityUpdateContent } from './notificationEventContent/opportunityUpdateContent';
import { VisaContent } from './notificationEventContent/visa';
import { IncorrectLoginContent } from './notificationEventContent/incorrectLogin';
import { UnconfirmedUserContent } from './notificationEventContent/unconfirmedUser';
import { UserNotConfirmedContent } from './notificationEventContent/userNotConfirmed';
import { SupportCaseContent } from './notificationEventContent/supportCaseContent';
import { DraftApplicationExistingUserContent } from './notificationEventContent/draftApplicationExistingUser';
import { DraftApplicationNewUserContent } from './notificationEventContent/draftApplicationNewUser';
import { WelcomeStudentDraftContent } from './notificationEventContent/welcomeStudentDraft';
import { ApplicationSubmittedByStudentContent } from './notificationEventContent/applicationSubmittedByStudent';
import { ApplicationSubmittedByAgentContent } from './notificationEventContent/applicationSubmittedByAgent';
@Injectable()
export class NotificationContentBuilder {
  constructor(
    @Inject(AdmissionStatusUpdateContent)
    private readonly admissionStatusUpdateContent: AdmissionStatusUpdateContent,
    private readonly welcomeStudentContent: WelcomeStudentContent,
    private readonly dynamoDBService: DynamoDBService,
    private readonly authService: AuthService,
    private readonly reviewCenterContent: ReviewCenterContent,
    private readonly admissionConditionContent: AdmissionConditionContent,
    private readonly issuedLetterContent: IssuedLetterContent,
    private readonly admissionLetterDeletion: AdmissionLetterDeletion,
    private readonly agentUploadDocumentContent: AgentUploadDocumentContent,
    private readonly salesforceService: SalesforceService,
    @Inject(forwardRef(() => OpportunityService))
    private readonly opportunityService: OpportunityService,
    private readonly s3Service: S3Service,
    private readonly cobrandingService: CobrandingService,
    private readonly closedTask: ClosedTask,
    private readonly joinAppheroContent: JoinAppheroContent,
    private readonly trackNewApplicationContent: TrackNewApplicationContent,
    private readonly taskReopen: TaskReopen,
    private readonly opportunityUpdateContent: OpportunityUpdateContent,
    private readonly visaContent: VisaContent,
    private readonly incorrectLoginContent: IncorrectLoginContent,
    private readonly unconfirmedUserContent: UnconfirmedUserContent,
    private readonly userNotConfirmedContent: UserNotConfirmedContent,
    private readonly supportCaseContent: SupportCaseContent,
    private readonly draftApplicationExistingUserContent: DraftApplicationExistingUserContent,
    private readonly draftApplicationNewUserContent: DraftApplicationNewUserContent,
    private readonly optimizedNotificationContentBuilder: OptimizedNotificationContentBuilder,
    private readonly welcomeStudentDraftContent: WelcomeStudentDraftContent,
    private readonly applicationSubmittedByStudentContent: ApplicationSubmittedByStudentContent,
    private readonly applicationSubmittedByAgentContent: ApplicationSubmittedByAgentContent,
  ) {}
  async getInAppEventText(
    event: NotificationEvent,
    eventDetail: NotificationEventDetails,
  ): Promise<string> {
    console.log('get content');
    const content: INotificationContent = await this.getNotificationContent(
      event,
    );
    console.log('get content', content);
    return await this.replacePlaceholders(content?.inAppMessage, eventDetail);
  }

  async getEmailNotificationContent(
    email: string,
    event: NotificationEvent,
    eventDetail: NotificationEventDetails,
  ): Promise<EmailTemplate> {
    console.log('Inside getEmailNotificationContent');
    const content: INotificationContent = await this.getNotificationContent(
      event,
      eventDetail.studentBrand,
      email,
    );

    if (eventDetail?.taskCreatedAt) {
      const date = new Date(eventDetail?.taskCreatedAt);
      const day = date.getDate().toString().padStart(2, '0');
      const month = date.toLocaleString('en-US', { month: 'long' });
      const year = date.getFullYear();
      eventDetail.taskCreatedAt = `${day}-${month},${year}`;
    }

    console.log('Event detail -->', eventDetail);
    if (content.emailBody && content.emailSubject) {
      return {
        body: await this.replacePlaceholders(content.emailBody, eventDetail),
        subject: await this.replacePlaceholders(
          content.emailSubject,
          eventDetail,
        ),
      };
    }
    return null;
  }

  async getNotificationContent(
    event: any,
    brand?: any,
    email?: any,
  ): Promise<INotificationContent> {
    let notificationContent;
    switch (event) {
      case 'ADMISSION_STATUS_UPDATE':
        notificationContent = this.admissionStatusUpdateContent;
        break;
      case 'WELCOME_STUDENT':
        notificationContent = this.welcomeStudentContent;
        break;
      case 'WELCOME_STUDENT_DRAFT':
        notificationContent = this.welcomeStudentDraftContent;
        break;
      case 'REVIEW_CENTER_COMMENT':
        notificationContent = this.reviewCenterContent;
        break;
      case 'ADMISSION_CONDITION':
        notificationContent = this.admissionConditionContent;
        break;
      case 'ISSUED_LETTERS':
        notificationContent = this.issuedLetterContent;
        break;
      case 'AGENT_UPLOADED_DOCUMENT':
        notificationContent = this.agentUploadDocumentContent;
        break;
      case 'TASK_CLOSURE':
        notificationContent = this.closedTask;
        break;
      case 'APPLICATION_SUBMITTED_JOIN_APPHERO':
        notificationContent = this.joinAppheroContent;
        break;
      case 'ADMISSION_LETTER_DELETION':
        notificationContent = this.admissionLetterDeletion;
        break;
      case 'TASK_REOPEN':
        notificationContent = this.taskReopen;
        break;
      case 'APPLICATION_SUBMITTED_TRACK_NEW_APPLICATION':
        notificationContent = this.trackNewApplicationContent;
        break;
      case 'APPLICATION_SUBMITTED_BY_STUDENT':
        notificationContent = this.applicationSubmittedByStudentContent;
        break;
      case 'APPLICATION_SUBMITTED_BY_AGENT':
        notificationContent = this.applicationSubmittedByAgentContent;
        break;
      case 'OPPORTUNITY_UPDATE':
        notificationContent = this.opportunityUpdateContent;
        break;
      case 'SAVE_VISA':
        notificationContent = this.visaContent;
        break;
      case 'INCORRECT_LOGIN':
        notificationContent = this.incorrectLoginContent;
        break;
      case 'UNCONFIRMED_USER':
        notificationContent = this.unconfirmedUserContent;
        break;
      case 'USER_NOT_CONFIRMED_LOGIN':
        notificationContent = this.userNotConfirmedContent;
        break;
      case 'SUPPORT_CASE_CREATED':
        notificationContent = this.supportCaseContent;
        break;
      case 'DRAFT_APPLICATION_EXISTING_USER':
        notificationContent = this.draftApplicationExistingUserContent;
        break;
      case 'DRAFT_APPLICATION_NEW_USER':
        notificationContent = this.draftApplicationNewUserContent;
        break;
      default:
        console.log(`Unsupported notification event: ${event}`);
        break;
    }
    if (brand) {
      notificationContent['emailBody'] = await this.getEmailContent(
        event,
        email,
        brand,
      );
    }
    return notificationContent;
  }
  async getEmailContent(event, email, brand): Promise<any> {
    console.log('Brand ->', brand);
    let loginUrl =
      brand === 'APPHERO'
        ? process.env.APPHERO_LOGIN_URL
        : await this.cobrandingService.generateLink(email, brand);

    const updateAccountDetailsPayload = {
      email,
      businessUnitFilter: brand,
      updateAccountDetails: {
        Cobranding_Link__c: loginUrl,
      },
    };

    await this.saveCobrandingLink(event, updateAccountDetailsPayload);

    let headerBanner = `${process.env.APPHERO_BRAND_LOGOS_BUCKET_URL}/apphero-cobranding-brands-email-banners/HEADERS/${brand}/${event}.png`;
    const footerBanner = `${process.env.APPHERO_BRAND_LOGOS_BUCKET_URL}/apphero-cobranding-brands-email-banners/FOOTERS/${brand}.png`;
    const cobrandingLogo = `${process.env.APPHERO_BRAND_LOGOS_BUCKET_URL}/apphero-cobranding-brands-email-banners/LOGOS/${brand}.png`;
    const appheroSignUpUrl = `${process.env.APPHERO_APP_ENDPOINT}/sign-up`;
    const appheroForgotPasswordUrl = `${process.env.APPHERO_APP_ENDPOINT}/forgot-password`;
    let key = `apphero-cobranding-notification-templates/${event}.html`;

    console.log('headerBanner', headerBanner);
    console.log('footerBanner', footerBanner);

    let emailBody = await this.s3Service.getHtmlFileFromS3({
      Bucket: process.env.APPHERO_BRAND_LOGOS_BUCKET,
      Key: key,
    });
    return emailBody
      .replace(/{{headerBanner}}/g, headerBanner)
      .replace(/{{footerBanner}}/g, footerBanner)
      .replace(/{{cobrandingLogo}}/g, cobrandingLogo)
      .replace(/{{cobrandingLoginUrl}}/g, loginUrl)
      .replace(/{{appheroSignUpUrl}}/g, appheroSignUpUrl)
      .replace(/{{appheroForgotPasswordUrl}}/g, appheroForgotPasswordUrl);
  }
  async getAdditionalInfo(
    messageDetails: any,
    email: string,
    event: any,
  ): Promise<EventAdditionalDetail> {
    // Use optimized direct Salesforce calls instead of middleware
    const additionalInfo =
      await this.optimizedNotificationContentBuilder.getAdditionalInfoOptimized(
        messageDetails,
        email,
        event,
      );

    return additionalInfo;
  }

  async saveInDb(event, payload: NotificationEventDetail): Promise<void> {
    switch (event) {
      case 'ADMISSION_STATUS_UPDATE':
        await this.updateOpportunity(
          payload.email,
          payload.messageDetails.opportunityId,
          {
            StageName: payload.messageDetails.stage,
            Student_Placement_Status__c: payload.messageDetails.studentStatus,
          },
        );
        break;
      case 'DRAFT_APPLICATION_EXISTING_USER':
        await this.opportunityService.refreshOpportunityFromSalesforce(
          payload.email,
          payload.messageDetails.opportunityId,
        );
        break;
      case 'REVIEW_CENTER_COMMENT':
        const [
          Document_Type__c,
          Rejection_Reason__c,
          Rejection_Reason_Comment__c,
          Related_Record_Name__c,
          Related_Record_Id__c,
        ] = payload.messageDetails.comment.split('|').map((str) => str.trim());

        await this.dynamoDBService.putObject(process.env.APPHERO_TASK_TABLE, {
          Item: {
            PK: payload.messageDetails.opportunityId,
            SK: payload.messageDetails.taskId,
            Id: payload.messageDetails.taskId,
            createdAt: new Date().toISOString(),
            WhatId: payload.messageDetails.opportunityId,
            Status: 'Open',
            isRead: false,
            Subject: payload.messageDetails.admissionsCondition,
            Description: payload.messageDetails.comment,
            CreatedDate: payload.messageDetails.taskCreatedAt,
            Follow_Up__c: payload.messageDetails.isMultiUpload,
            isDocumentUploaded: false,
            Duplicate_Task__c: false,
            Document_Type__c:
              payload.messageDetails.documentType !== 'null'
                ? payload.messageDetails.documentType
                : null,
            Related_Record_Name__c:
              Related_Record_Name__c && Related_Record_Name__c !== 'null'
                ? Related_Record_Name__c
                : null,
            Related_Record_Id__c:
              Related_Record_Id__c && Related_Record_Id__c !== 'null'
                ? Related_Record_Id__c
                : null,
          },
        });
        break;
      case 'ADMISSION_CONDITION':
        await this.updateOpportunity(
          payload.email,
          payload.messageDetails.opportunityId,
          {
            Admissions_Condition__c: payload.messageDetails.admissionsCondition,
          },
        );
        break;
      case 'ISSUED_LETTERS':
        await this.createOpportunityFile(payload);
        break;
      case 'AGENT_UPLOADED_DOCUMENT':
        await this.createOpportunityFile(payload);
        break;
      case 'TASK_CLOSURE':
        const existingTask = await this.dynamoDBService.getObject(
          process.env.APPHERO_TASK_TABLE,
          {
            PK: payload.messageDetails.opportunityId,
            SK: payload.messageDetails.taskId,
          },
        );
        console.log('existingTask ->', existingTask);
        if (existingTask.Item) {
          await this.dynamoDBService.updateObject(
            process.env.APPHERO_TASK_TABLE,
            {
              PK: payload.messageDetails.opportunityId,
              SK: payload.messageDetails.taskId,
            },
            {
              Status: 'Completed',
              IsAgentClosed: payload.messageDetails.taskClosedBy === 'Agent',
              IsDirectSalesClosed:
                payload.messageDetails.taskClosedBy === 'Direct',
            },
          );
        } else {
          await this.dynamoDBService.putObject(process.env.APPHERO_TASK_TABLE, {
            Item: {
              PK: payload.messageDetails.opportunityId,
              SK: payload.messageDetails.taskId,
              Description: payload.messageDetails.comment,
              Id: payload.messageDetails.taskId,
              createdAt: new Date().toISOString(),
              WhatId: payload.messageDetails.opportunityId,
              Status: 'Completed',
              isRead: false,
              Subject: payload.messageDetails.admissionsCondition,
              CreatedDate: payload.messageDetails.taskCreatedAt,
              IsAgentClosed: payload.messageDetails.taskClosedBy === 'Agent',
              IsDirectSalesClosed:
                payload.messageDetails.taskClosedBy === 'Direct',
              Document_Type__c:
                payload.messageDetails.documentType !== 'null'
                  ? payload.messageDetails.documentType
                  : null,
            },
          });
        }

        console.log('Start ..');
        const openTasks =
          await this.opportunityService.getOpenTasksByOpportunityId(
            payload.messageDetails.opportunityId,
          );
        console.log('openTasks -->', openTasks.length);
        if (openTasks.length === 0) {
          await this.closeNotification(payload.email);
        }
        break;
      case 'AGENT_TASK_CANCEL':
        await this.dynamoDBService.updateObject(
          process.env.APPHERO_TASK_TABLE,
          {
            PK: payload.messageDetails.opportunityId,
            SK: payload.messageDetails.taskId,
          },
          {
            Status: 'Cancelled',
            IsAgentCancelled: true,
          },
        );

        console.log('Start ..');
        const openTaskDetails =
          await this.opportunityService.getOpenTasksByOpportunityId(
            payload.messageDetails.opportunityId,
          );
        console.log('openTasks -->', openTaskDetails.length);
        if (openTaskDetails.length === 0) {
          await this.closeNotification(payload?.email);
        }
        break;

      case 'OPPORTUNITY_UPDATE':
        const opportunityFields = payload.messageDetails.opportunityFields;
        const updateFields = {};

        const fieldMappings = {
          ownerEmail: 'OwnerEmail__c',
          ownerName: 'OwnerName__c',
          studyMode: 'Study_Mode__c',
          phone: 'Owner.Phone',
          location: 'Location__c',
          overallStartDate: 'OverallStartDate__c',
          intakeDate: 'Product_Intake_Date__c',
          bookingLink: 'Owner.Appointment_Booking_Link__c',
          deliveryMode: 'Delivery_Mode__c',
        };

        for (const [key, sfField] of Object.entries(fieldMappings)) {
          const value = opportunityFields[key];

          if (sfField.includes('.')) {
            if (
              opportunityFields.ownerEmail === null &&
              (value === null || value === undefined)
            ) {
              continue;
            }

            const [parentField, childField] = sfField.split('.');
            updateFields[parentField] = updateFields[parentField] || {};
            updateFields[parentField][childField] = value;
            continue;
          }

          if (key !== 'ownerName' && (value === null || value === undefined)) {
            continue;
          }
          if (['overallStartDate', 'intakeDate'].includes(key)) {
            updateFields[sfField] = new Date(value).toISOString().split('T')[0];
          } else if (key === 'ownerName') {
            if (opportunityFields.ownerEmail !== null) {
              updateFields[sfField] = value;
            }
          } else {
            updateFields[sfField] = value;
          }
        }
        await this.updateOpportunity(
          payload.email,
          payload.messageDetails.opportunityId,
          updateFields,
        );
        break;

      case 'ADMISSION_LETTER_DELETION':
        const deleteDocumentParams = {
          TableName: process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
          Key: {
            PK: payload.messageDetails.opportunityId,
            SK: payload.messageDetails.opportunityFileId,
          },
        };
        await this.dynamoDBService.deleteObject(deleteDocumentParams);
        await this.closeIssuedLetterNotification(
          payload.email,
          payload.messageDetails,
        );
        break;

      case 'TASK_REOPEN':
        const { opportunityId, taskId } = payload.messageDetails;

        await this.dynamoDBService.updateObject(
          process.env.APPHERO_TASK_TABLE,
          { PK: opportunityId, SK: taskId },
          {
            Status: 'Open',
            IsAgentClosed: false,
            IsDirectSalesClosed: false,
            IsAgentCancelled: false,
          },
        );

        const tasksNotificationByEmail =
          await this.fetchOpenTaskNotificationByEmail(payload.email, true);
        const notificationsByOpportunity = tasksNotificationByEmail.filter(
          (item) => item.messageDetails.opportunityId === opportunityId,
        );

        const hasOpenNotification = notificationsByOpportunity.some(
          (item) => !item.isClosed,
        );

        if (!hasOpenNotification && notificationsByOpportunity.length > 0) {
          await this.dynamoDBService.updateObject(
            process.env.APPHERO_NOTIFICATION_TABLE,
            {
              PK: payload.email,
              SK: notificationsByOpportunity[0].SK,
            },
            { isClosed: false },
          );
        }
        break;

      case 'WELCOME_STUDENT_DRAFT':
      case 'WELCOME_STUDENT':
        // Use optimized direct Salesforce calls instead of middleware
        await this.optimizedNotificationContentBuilder.processWelcomeStudentOptimized(
          payload,
        );
        break;
      case 'SAVE_VISA':
        const visaFieldMap = {
          visaId: 'Id',
          visaApplicationDate: 'Visa_Application_Date__c',
          visaApplicationReferenceNumber:
            'Visa_Application_Reference_Number__c',
          visaApplicationStatus: 'Visa_Application_Status__c',
          visaInterviewDate: 'Visa_Interview_Date__c',
          visaNumber: 'Visa_Number__c',
          arrivalDate: 'Arrival_Date__c',
          visaRequired: 'Visa_Required__c',
        };

        const visaFields = payload.messageDetails?.visaFields || {};
        const visaDetails = Object.entries(visaFieldMap).reduce(
          (acc, [key, mappedKey]) => {
            const value = visaFields[key];
            if (value != null) {
              acc[mappedKey] = value;
            }
            return acc;
          },
          {},
        );

        const updateRequest = { Visa_Application__r: { records: [] } };
        const existingRecords = payload.messageDetails?.visaRecords || [];

        if (existingRecords.length > 0) {
          let recordUpdated = false;

          const updatedRecords = existingRecords.map((record) => {
            if (record.Id === visaDetails['Id']) {
              recordUpdated = true;
              return { ...record, ...visaDetails };
            }
            return record;
          });

          if (!recordUpdated) {
            updatedRecords.push(visaDetails);
          }

          updateRequest.Visa_Application__r.records = updatedRecords;
        } else {
          updateRequest.Visa_Application__r.records = [visaDetails];
        }
        // Update opportunity
        await this.updateOpportunity(
          payload.email,
          payload.messageDetails.opportunityId,
          updateRequest,
        );
        if (payload.messageDetails.opportunityFileId) {
          await this.createOpportunityFile(payload);
        }

        break;

      default:
        console.log('No save for this event');
        break;
    }
  }
  async checkCanSendNotification(
    additionalDetails: EventAdditionalDetail,
    event,
    notificationInputDetails: NotificationInput,
  ): Promise<boolean> {
    let notificationObject: NotificationEventDetail;
    switch (event) {
      case 'ADMISSION_STATUS_UPDATE':
        if (
          notificationInputDetails.messageDetails.appHeroStage ===
          additionalDetails.currentStage
        ) {
          return false;
        } else {
          return true;
        }
      case 'REVIEW_CENTER_COMMENT':
        const {
          email,
          messageDetails: { opportunityId, taskId, isSubsequentComment },
        } = notificationInputDetails;

        notificationObject = {
          email,
          event,
          messageDetails: {
            ...notificationInputDetails.messageDetails,
            ...additionalDetails,
          },
        };
        let openNotification = [];
        if (!isSubsequentComment) {
          const openNotificationByEmail =
            await this.fetchOpenTaskNotificationByEmail(email);
          console.log('openNotificationByEmail', openNotificationByEmail);
          openNotification = openNotificationByEmail.filter(
            (item) => item.messageDetails.opportunityId === opportunityId,
          );
          await this.saveInDb(event, notificationObject);
          console.log('openNotification', openNotification);
        } else {
          await this.saveInDb(event, notificationObject);
          return false;
        }

        return openNotification.length === 0;

      case 'AGENT_TASK_CANCEL':
        notificationObject = {
          email: notificationInputDetails.email,
          event,
          messageDetails: {
            ...notificationInputDetails.messageDetails,
            ...additionalDetails,
          },
        };
        await this.saveInDb(event, notificationObject);
        return false;
      case 'ADMISSION_LETTER_DELETION':
        notificationObject = {
          email: notificationInputDetails.email,
          event,
          messageDetails: {
            ...notificationInputDetails.messageDetails,
            ...additionalDetails,
          },
        };

        await this.saveInDb(event, notificationObject);
        return true;

      default:
        return true;
    }
  }
  async replacePlaceholders(
    template: string,
    values: NotificationEventDetails,
  ): Promise<string> {
    return template?.replace(/{{(.*?)}}/g, (_, key) => values[key] || '');
  }
  async updateOpportunity(email, opportunityId, payload) {
    try {
      const tableName = process.env.APPHERO_SF_OPPORTUNITY_TABLE;
      const key = {
        PK: email,
        SK: opportunityId,
      };

      payload.updatedAt = new Date().toISOString();
      console.log('payload -->', payload, key);
      await this.dynamoDBService.updateObject(tableName, key, payload);

      console.log('updated');
    } catch (error) {
      console.log('error -->', error);
      throw error;
    }
  }

  async createOpportunityFile(payload): Promise<any> {
    let opportunityFile = await this.salesforceService.fetchData(
      `gus/opportunityfile/${payload.messageDetails.opportunityFileId}`,
    );
    opportunityFile = opportunityFile[0];
    console.log('opportunityFile-->', JSON.stringify(opportunityFile));
    if (opportunityFile) {
      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
        {
          Item: {
            PK: payload.messageDetails.opportunityId,
            SK: opportunityFile?.Id,
            ...opportunityFile,
            createdAt: new Date().toISOString(),
          },
        },
      );
    } else {
      throw new NotFoundException(
        `OpportunityFile Not found - ${payload.messageDetails.opportunityFileId}`,
      );
    }
  }

  async closeNotification(email: string): Promise<any> {
    let params = {
      TableName: process.env.APPHERO_NOTIFICATION_TABLE,
      KeyConditionExpression: `PK = :pk and begins_with(SK, :sk)`,
      FilterExpression: `(attribute_not_exists(#isClosed) OR #isClosed = :isClosed) and #event = :event`,
      ExpressionAttributeNames: {
        '#isClosed': 'isClosed',
        '#event': 'event',
      },
      ExpressionAttributeValues: {
        ':pk': email,
        ':sk': 'INAPP#',
        ':event': 'REVIEW_CENTER_COMMENT',
        ':isClosed': false,
      },
    };

    console.log('Notification Params ->', params);
    const notificationDetails = await this.dynamoDBService.queryObjects(params);
    console.log(notificationDetails.Items);

    if (notificationDetails?.Items?.length > 0) {
      for (const item of notificationDetails.Items) {
        const updateNotificationDetails =
          await this.dynamoDBService.updateObject(
            process.env.APPHERO_NOTIFICATION_TABLE,
            {
              PK: email,
              SK: item.SK,
            },
            {
              isClosed: true,
            },
          );
        console.log(
          'updateNotificationDetails for SK:',
          item.SK,
          updateNotificationDetails,
        );
      }
    } else {
      console.log('No open notification found for the emailId');
    }
  }

  async closeIssuedLetterNotification(
    email: string,
    messageDetails: any,
  ): Promise<any> {
    let params = {
      TableName: process.env.APPHERO_NOTIFICATION_TABLE,
      KeyConditionExpression: `PK = :pk and begins_with(SK, :sk)`,
      FilterExpression: `(attribute_not_exists(#isClosed) OR #isClosed = :isClosed) and #event = :event`,
      ExpressionAttributeNames: {
        '#isClosed': 'isClosed',
        '#event': 'event',
      },
      ExpressionAttributeValues: {
        ':pk': email,
        ':sk': 'INAPP#',
        ':event': 'ISSUED_LETTERS',
        ':isClosed': false,
      },
    };

    console.log('Notification Params ->', params);
    const notificationDetails = await this.dynamoDBService.queryObjects(params);
    console.log('notificationDetails  ->', notificationDetails?.Items);

    if (notificationDetails?.Items?.length > 0) {
      for (const item of notificationDetails.Items) {
        if (
          item.messageDetails.opportunityFileId ===
          messageDetails.opportunityFileId
        ) {
          const updateNotificationDetails =
            await this.dynamoDBService.updateObject(
              process.env.APPHERO_NOTIFICATION_TABLE,
              {
                PK: email,
                SK: item.SK,
              },
              {
                isClosed: true,
              },
            );
          console.log(
            'updateNotificationDetails for SK:',
            item.SK,
            updateNotificationDetails,
          );
        }
      }
    } else {
      console.log('No open notification found for the emailId');
    }
  }

  async fetchOpenTaskNotificationByEmail(email, fetchAllTasks = false) {
    const params = {
      TableName: process.env.APPHERO_NOTIFICATION_TABLE,
      KeyConditionExpression: 'PK = :email',
      FilterExpression:
        '#eventType = :reviewCenter AND #notificationType = :inApp' +
        (fetchAllTasks ? '' : ' AND isClosed = :false'),
      ExpressionAttributeNames: {
        '#eventType': 'event',
        '#notificationType': 'type',
      },
      ExpressionAttributeValues: {
        ':email': email,
        ':reviewCenter': 'REVIEW_CENTER_COMMENT',
        ':inApp': 'INAPP',
        ...(fetchAllTasks ? {} : { ':false': false }),
      },
      ScanIndexForward: false,
    };

    const { Items } = await this.dynamoDBService.queryObjects(params);
    return Items;
  }

  async saveCobrandingLink(event: string, updateAccountDetailsPayload: any) {
    if (
      updateAccountDetailsPayload.brand !== 'APPHERO' &&
      (event === 'APPLICATION_SUBMITTED_TRACK_NEW_APPLICATION' ||
        event === 'APPLICATION_SUBMITTED_JOIN_APPHERO')
    ) {
      console.log(
        'Update Cobranding link to Salesforce Initiated with this payload',
        updateAccountDetailsPayload,
      );

      try {
        await this.salesforceService.patchData(
          'gus/updateaccount',
          updateAccountDetailsPayload,
        );

        console.log('Successfully updated Cobranding link to Salesforce');
      } catch (error) {
        console.log(
          'Error When updating Cobranding link to Salesforce ',
          error,
        );
      }
    }
  }
}
