import { S3Service } from 'apps/src/common/s3.service';
import { INotificationContent } from '../INotificationContent';
export class DraftApplicationExistingUserContent
  implements INotificationContent
{
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;

  constructor(private s3Service: S3Service, private brand: string) {
    this.inAppMessage =
      'A new application has been initiated in your AppHero account!';
    this.emailSubject = `A New Application Has Been Initiated in Your AppHero Account!`;
  }
}
