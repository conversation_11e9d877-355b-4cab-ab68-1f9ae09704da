import { INotificationContent } from '../INotificationContent';
export class ApplicationSubmittedByStudentContent
  implements INotificationContent
{
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage = `Congratulations! Your application to {{institution}} has been submitted sucessfully.`;
    this.emailSubject = `Your Application to {{institution}} has been submitted.`;
  }
}
