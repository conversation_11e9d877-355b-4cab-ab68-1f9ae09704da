import { INotificationContent } from '../INotificationContent';
export class ApplicationSubmittedByAgentContent
  implements INotificationContent
{
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage = `Congratulations! Your application to {{institution}} has been submitted by the agent.`;
    this.emailSubject = `Your Application to {{institution}} Has Been Submitted!`;
  }
}
