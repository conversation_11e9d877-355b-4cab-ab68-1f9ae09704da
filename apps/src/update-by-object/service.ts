import {
  Injectable,
  BadRequestException,
  NotFoundException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class updateByObjectService {
  constructor(private readonly dynamoDBService: DynamoDBService) {}

  /**
   * Maps Salesforce object names to database table environment variable names
   * @param objectType The Salesforce object type (e.g., 'opportunity', 'Account')
   * @returns The environment variable name for the corresponding table
   */
  private getTableNameFromEnv(objectType: string): string {
    // Convert object type to uppercase and create env variable pattern
    const normalizedObjectType = objectType.toLowerCase();

    // Define mapping of SF object names to environment variable patterns
    const objectToEnvMap: Record<string, string> = {
      opportunity: 'APPHERO_SF_OPPORTUNITY_TABLE',
    };

    const envVarName = objectToEnvMap[normalizedObjectType];

    if (!envVarName) {
      throw new BadRequestException(
        `Unsupported object type: ${objectType}. Supported types: ${Object.keys(
          objectToEnvMap,
        ).join(', ')}`,
      );
    }

    const tableName = process.env[envVarName];

    if (!tableName) {
      throw new NotFoundException(
        `Environment variable ${envVarName} not found for object type ${objectType}`,
      );
    }

    return tableName;
  }

  /**
   * Determines the primary key structure based on object type
   * @param objectType The Salesforce object type
   * @param payload The update payload
   * @returns Object containing PK and SK for DynamoDB
   */
  private buildPrimaryKey(
    objectType: string,
    payload: any,
  ): { PK: string; SK: string } {
    const normalizedObjectType = objectType.toLowerCase();
    const recordId = payload.Id || payload.id;

    switch (normalizedObjectType) {
      case 'opportunity':
        // For opportunities, we need email as PK and Id as SK
        if (!payload.email && !payload.Email) {
          throw new BadRequestException(
            'Email field is required for opportunity updates',
          );
        }
        return {
          PK: (payload.email || payload.Email).toLowerCase(),
          SK: recordId,
        };

      default:
        throw new BadRequestException(
          `Unsupported object type: ${objectType}. Supported types: opportunity`,
        );
    }
  }

  /**
   * Updates a Salesforce object in the database
   * @param objectType The type of Salesforce object (e.g., 'opportunity', 'Account')
   * @param payload The update payload from request body
   * @param request The Express request object
   * @returns Promise<any> The update result
   */
  async updateSfObject(
    objectType: string,
    payload: any,
    request: Request,
  ): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();

    try {
      // Log the update initiation
      console.log(
        `SF Update: Initiated ${objectType} update for request ${requestId}`,
      );

      // Get the table name from environment variables
      const tableName = this.getTableNameFromEnv(objectType);

      // Build the primary key for the record
      const primaryKey = this.buildPrimaryKey(objectType, payload);

      // Remove Id from payload to avoid conflicts (it's used in the key)
      const updatePayload = { ...payload };
      delete updatePayload.Id;
      delete updatePayload.id;
      delete updatePayload.email;
      delete updatePayload.Email;
      delete updatePayload.opportunityId;
      delete updatePayload.OpportunityId;

      // Perform the update
      await this.dynamoDBService.updateObject(
        tableName,
        primaryKey,
        updatePayload,
      );

      // Log successful update
      console.log(
        `SF Update: Successfully updated ${objectType} with ID: ${primaryKey.SK} for request ${requestId}`,
      );

      return {
        success: true,
        message: `${objectType} updated successfully`,
        recordId: primaryKey.SK,
        updatedFields: Object.keys(updatePayload),
      };
    } catch (error) {
      // Log the error
      console.error(
        `SF Update: Failed to update ${objectType}: ${error.message} for request ${requestId}`,
        error.stack,
      );

      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      throw new HttpException(
        `Failed to update ${objectType}: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
