import { Module } from '@nestjs/common';
import { CommonModule } from '../common/module';
import { AccountController } from './controller';
import { AccountService } from './service';
import { OptimizedAccountService } from './optimized-account.service';
import { LoggerModule } from '@gus-eip/loggers';
import { OpportunityModule } from '../opportunity/module';

@Module({
  imports: [CommonModule, OpportunityModule],
  controllers: [AccountController],
  providers: [AccountController, AccountService, OptimizedAccountService],
  exports: [AccountController, AccountService],
})
export class AccountModule {}
