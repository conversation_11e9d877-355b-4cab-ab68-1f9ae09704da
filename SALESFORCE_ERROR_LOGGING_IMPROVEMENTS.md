# Salesforce Error Logging Improvements

## Overview

Enhanced the error handling and logging in `src/handlers/appheroSyncOnDemand.js` to provide comprehensive error capture and logging for Salesforce API responses. The improvements ensure that when Salesforce operations fail, we can easily identify what went wrong from the logs.

## Issues Resolved

1. **Multiple Alerts for Single Error**: Reduced from 3 alerts per error to 1 comprehensive alert
2. **Insufficient Salesforce Error Details**: Now captures detailed Salesforce-specific error information including error codes, field names, line/column numbers, duplicate rule details, etc.
3. **Poor Error Context**: Enhanced with query context, record counts, and structured error classification

## Key Improvements

### 1. Enhanced Salesforce Response Logging Function

- **Location**: New `logSalesforceResponse()` function in `appheroSyncOnDemand.js`
- **Purpose**: Centralized, comprehensive logging for all Salesforce API responses
- **Features**:
  - Detects null/undefined responses
  - Identifies successful array responses
  - Parses direct Salesforce error structures
  - Handles composite response errors
  - Warns about unexpected response structures
  - Provides structured error details with context

### 2. Error Detection Capabilities

The enhanced logging now properly detects and handles:

#### Null/Undefined Responses

```javascript
// Detects when Salesforce API returns null or undefined
if (!response) {
  // Logs detailed error with context
}
```

#### Successful Responses

```javascript
// Identifies successful array responses
if (Array.isArray(response)) {
  // Logs success with record count
}
```

#### Direct Salesforce Errors

```javascript
// Parses Salesforce error structures
if (response.error || response.errorCode || response.message) {
  // Extracts errorCode, message, fields, statusCode
}
```

#### Composite Response Errors

```javascript
// Handles composite request errors
if (response.compositeResponse && hasErrors) {
  // Logs detailed error information for each failed request
}
```

### 3. Improved Error Context

Each error log now includes:

- **Timestamp**: ISO timestamp for precise error timing
- **Entity Type**: Which Salesforce entity was being processed
- **Query Context**: Truncated query for debugging (first 200 chars)
- **Record Count**: Number of records being processed
- **Error Details**: Structured error information
- **Response Type**: Classification of the error type

### 4. Enhanced Batch Processing Error Handling

- **Location**: `executeQueryInBatches()` function
- **Improvements**:
  - Detailed batch error logging with batch index
  - Individual error parsing for each error in batch responses
  - Better error context with batch information

### 5. Improved Composite Query Request Handling

- **Location**: `compositeQueryRequest()` function
- **Improvements**:
  - Pre-validation of composite responses before returning
  - Detailed error logging for failed composite requests
  - Better handling of unexpected response structures

### 6. Entity-Specific Error Handling

Added comprehensive try-catch blocks and error accumulation for all entities:

#### Account Entity

- Enhanced logging with email count context
- Proper error accumulation and success tracking

#### Task Entity

- Added try-catch wrapper with error accumulation
- Context includes WhatId count and table information

#### Opportunity Entity

- Enhanced error handling with email count context
- Proper success/failure tracking

#### OpportunityFile Entity

- Added comprehensive error handling
- Context includes opportunity ID count

#### Application\_\_c Entity

- Enhanced error handling with B2C opportunity ID context
- Proper error accumulation

#### OpportunityDocumentType\_\_c Entity

- Added error handling with brand count context
- Enhanced logging integration

### 7. Structured Error Accumulation

All Salesforce operations now properly use the ErrorAccumulator:

- **Success Tracking**: `errorAccumulator.addSuccess()`
- **Error Tracking**: `errorAccumulator.addError()` with detailed context
- **Warning Tracking**: `errorAccumulator.addWarning()` for unexpected structures

### 8. Consistent Logging Format

All logs now follow a consistent format:

```
[TIMESTAMP] MESSAGE { contextObject }
```

## Benefits

### 1. Improved Debugging

- **Before**: Simple `console.log("salesforceRecordResponse", response)`
- **After**: Structured error detection with detailed context and error classification

### 2. Better Error Visibility

- Clear distinction between different types of errors
- Structured error details that are easy to parse and understand
- Comprehensive context for each error

### 3. Enhanced Monitoring

- Proper integration with existing ErrorAccumulator and LoggerService
- Structured data that can be easily monitored and alerted on
- Comprehensive error summaries at the end of processing

### 4. Fault Tolerance

- Processing continues even when individual Salesforce operations fail
- Detailed error tracking without stopping the entire sync process
- Clear success/failure metrics for each entity type

## Error Types Detected

1. **Null Response**: When Salesforce API returns null/undefined
2. **Salesforce API Errors**: Direct error responses from Salesforce
3. **Composite Request Errors**: Errors in batch/composite requests
4. **HTTP Status Errors**: 400+ status codes in responses
5. **Unexpected Structures**: Responses that don't match expected formats

## Usage Example

The enhanced logging is automatically applied to all Salesforce API calls:

```javascript
const salesforceRecordResponse = await compositeQueryRequest(salesforceQuery);

// Enhanced logging is automatically applied
await logSalesforceResponse(
  salesforceRecordResponse,
  "Account",
  salesforceQuery,
  logger,
  errorAccumulator,
  requestId,
  emails?.length || 0
);
```

## Testing

The improvements have been tested with various error scenarios:

- ✅ Successful responses (arrays)
- ❌ Null/undefined responses
- ❌ Direct Salesforce errors
- ❌ Composite response errors
- ⚠️ Unexpected response structures

All test scenarios properly detected and logged the appropriate error types with detailed context.

## Alert Reduction Strategy

### Before: Multiple Alerts per Error

1. **Individual Error Logging**: Each Salesforce error logged separately to CloudWatch
2. **Comprehensive Report**: Overall sync status logged to CloudWatch
3. **Entity Summary Logging**: Per-entity error summaries logged to CloudWatch

**Result**: 3 alerts for 1 error ❌

### After: Single Comprehensive Alert

1. **Console Logging Only**: Individual errors logged to console with full details
2. **Error Accumulation**: Errors collected in ErrorAccumulator with enhanced context
3. **Single Comprehensive Report**: One CloudWatch alert with all error details

**Result**: 1 alert for 1 error ✅

## Enhanced Salesforce Error Details

### New Error Information Captured

- **Error Codes**: INVALID_FIELD, DUPLICATES_DETECTED, REQUIRED_FIELD_MISSING, etc.
- **SOQL Query Errors**: Line numbers, column numbers, compile problems
- **Duplicate Rule Errors**: Rule names, matching records, allow save flags
- **Validation Errors**: Field-specific validation failures
- **Rate Limiting**: Retry-after seconds, quota information
- **Composite Errors**: HTTP status codes, reference IDs, individual error details

### Example Enhanced Error Output

```
Salesforce Error 1:
  Operation: Account_Salesforce_Query
  Entity: Account
  Message: Salesforce API error for Account: INVALID_FIELD - No such column 'InvalidField__c'
  SF Error Code: INVALID_FIELD
  SF Message: No such column 'InvalidField__c' on entity 'Account'
  SF Fields: InvalidField__c
  SF Line: 1
  SF Column: 45
  SF Compile Problem: field 'InvalidField__c' can not be filtered in a query call
```

## Impact Summary

### Error Visibility: Before vs After

**Before:**

```
console.log("salesforceRecordResponse", salesforceRecordResponse);
// Result: Generic object dump, no error classification
```

**After:**

```
[2025-08-04T08:38:56.604Z] Salesforce API error for Account: INVALID_FIELD - No such column 'InvalidField__c'
[2025-08-04T08:38:56.604Z] Salesforce Error Details: {
  errorCode: "INVALID_FIELD",
  message: "No such column 'InvalidField__c' on entity 'Account'",
  fields: ["InvalidField__c"],
  line: 1,
  column: 45,
  compileProblem: "field 'InvalidField__c' can not be filtered in a query call"
}
```

### Alert Management: Before vs After

**Before:** 3 alerts per error = Alert fatigue ❌
**After:** 1 comprehensive alert per sync = Clear, actionable alerts ✅

### Debugging Efficiency: Before vs After

**Before:** Generic error messages, manual investigation required ❌
**After:** Specific Salesforce error codes, exact field names, line numbers ✅
