const { v4: uuidv4 } = require("uuid");
const axios = require("axios");
const eventTypes = require("../constants/eventTypes");
const dynamodbService = require("../common/dynamodb.service");

module.exports.handler = async (event) => {  
  // Process each event type
  for (const eventType of Object.values(eventTypes)) {
    console.log(`Processing event type: ${eventType}`);
    
    try {
      await processEventType(eventType);
    } catch (error) {
      console.error(`Error processing event type ${eventType}:`, error);
    }
  }
  
  return { message: "Notification processing completed" };
};

async function processEventType(eventType) {
  // Query for unconfirmed records of the given event type
  const params = {
    TableName: process.env.APPHERO_NOTIFICATIONS_TABLE,
    KeyConditionExpression: "PK = :eventType",
    FilterExpression: "#status = :status",
    ExpressionAttributeNames: {
      "#status": "status"
    },
    ExpressionAttributeValues: {
      ":eventType": eventType,
      ":status": "Unconfirmed"
    }
  };
  
  const result = await dynamodbService.queryObjects(params);
  if(result.Items.length === 0) {
    console.log(`No records found for ${eventType}`);
    return;
  }
  console.log(`Found ${result.Items.length} unconfirmed records for ${eventType}`);
  
  // Process each record
  for (const record of result.Items) {
    await processRecord(record, eventType);
  }
}

async function processRecord(record, eventType) {
  const scheduledAt = new Date(record.scheduledAt);
  const currentTime = new Date();
  
  // and if current time has passed the scheduled time
  if (currentTime >= scheduledAt) {
    console.log(`Sending notification for record: ${record.SK}`);
    
    // Send email notification
    const res = await sendEmailNotification(record.SK, eventType);
    if(res){
      await dynamodbService.deleteObject({
        TableName: process.env.APPHERO_NOTIFICATIONS_TABLE,
        Key: {
          PK: record.PK,
          SK: record.SK
        }
      });
    }
  } else {
    console.log(`Skipping record ${record.SK}: Not yet eligible for notification`);
  }
}

async function sendEmailNotification(email, eventType) {
  try {
    const messageId = uuidv4();
    const payload = {
      input: {
        email: email,
        event: eventType,
        messageDetails: {
          messageId: messageId
        }
      }
    };
    
    console.log(`Sending notification with payload:`, JSON.stringify(payload));
    
    // Call the email API
    const response = await axios.post(`${process.env.APPHERO_API_ENDPOINT}/unauth/apphero/createnotification`, payload);
    console.log(`Email API response:`, response.data);
    
    return response.data;
  } catch (error) {
    console.error(`Error sending email notification:`, error);
    throw error;
  }
}
