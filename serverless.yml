service: apphero-backend-service
provider:
  name: aws
  runtime: nodejs16.x
  stage: ${opt:stage, 'dev'}
  region: eu-west-1
  role: ${file(env.${self:provider.stage}.yml):role_arn}
  ecr:
    images:
      appimage:
        path: ./
custom:
  enablePITR:
    prod: true
    dev: false
  isPITREnabled: ${self:custom.enablePITR.${self:provider.stage}, false}
plugins:
  - serverless-appsync-plugin
resources:
  - ${file(permissions.yml)}
  - ${file(tables.yml)}
appSync: ${file(appsync.yml)}
functions:
  apphero-backend:
    name: 'apphero-backend-service-${self:provider.stage}'
    architecture: x86_64
    image:
      name: appimage
      command:
        - dist/src/main.handler
      entryPoint:
        - '/lambda-entrypoint.sh'
    timeout: 120
    environment: ${file(env.${self:provider.stage}.yml):variables}
    tags:
      ENVIRONMENT: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
      TEAM: EIP Development Team
      PROJECT: APPHERO
