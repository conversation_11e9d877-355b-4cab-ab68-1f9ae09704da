service: apphero-sf-sync-service
frameworkVersion: "3"

provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, 'dev'}
  region: "eu-west-1"
  environment: ${file(env.${sls:stage}.json)}

custom:
  enablePITR:
    prod: true
    dev: false
  isPITREnabled: ${self:custom.enablePITR.${sls:stage}, false}
  scheduleRate:
    dev: rate(1 hour)
    prod: rate(1 hour)

resources:
  - ${file(src/serverless-resources/roles.yml)}
  - ${file(src/serverless-resources/tables.yml)}
functions:
  - ${file(src/serverless-resources/functions.yml)}
