{"REGION": "eu-west-1", "APPHERO_SF_ACCOUNTS_TABLE": "apphero-sf-account-${sls:stage}", "APPHERO_SF_TASK_TABLE": "apphero-sf-task-${sls:stage}", "APPHERO_SF_OPPORTUNITY_TABLE": "apphero-sf-opportunity-${sls:stage}", "APPHERO_SF_OPPORTUNITYFILE_TABLE": "apphero-sf-opportunityfile-${sls:stage}", "SALESFORCE_API_VERSION": "v53.0", "GUS_CONSUMER_KEY": "3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W", "GUS_CONSUMER_SECRET": "****************************************************************", "GUS_AUTH_URL": "https://iapro.my.salesforce.com/services/oauth2/token", "GUS_USER_NAME": "<EMAIL>", "GUS_PASSWORD": "5@{`bdT!!W0zjLACxWqHDwEYk1EiYQRg2AL", "GUS_GRANT_TYPE": "password", "ACCESS_TOKEN_SECRET": "salesforce-gus-${sls:stage}-access-token", "APPHERO_SF_APPLICATION_TABLE": "apphero-sf-application-${sls:stage}", "APPHERO_CONSUMER_KEY": "xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ", "APPHERO_CONSUMER_TABLE": "gus-eip-consumer-${self:provider.stage}", "APPHERO_CMS_SYNC_CHANNEL_ID": "0apTf00000003E1IAI", "APPHERO_CMS_SYNC_TABLE_NAME": "apphero-sf-cms-content-${self:provider.stage}", "GET_TASK_FROM_DATE": "2024-12-16T00:00:00.000Z", "APPHERO_LOGS_EXPORTS_BUCKET_NAME": "apphero-logs-exports-prod", "GUS_MIDDLEWARE_API": "https://api.guseip.io", "GUS_MIDDLEWARE_API_KEY": "xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ", "APPHERO_NOTIFICATIONS_TABLE": "apphero-notifications-${sls:stage}", "APPHERO_API_ENDPOINT": "https://api.apphero.io", "LOGGER_LOG_GROUP_NAME": "apphero-logs-prod", "TEAMS_WEBHOOK_URL": "https://gus.webhook.office.com/webhookb2/f171ec91-8e4c-402d-9191-3af15323b980@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/6ddda97162464ec3bfd7779d74d84d9f/7f8dc40b-ecac-4851-a3c0-45c9ca0120b1/V2MqhNIJdm-KK-vhylBOHj0-ncn_1NrRqThl2vKdmeC_41"}